name: inkflow-ai

services:
  # 后端API服务
  backend:
    image: inkflow-ai-backend:latest
    container_name: inkflow-backend
    env_file:
      - .env
    environment:
      - DEBUG=false
    ports:
      - "20001:20001"

    networks:
      - inkflow-network
      - database-network
    # volumes:
    #   - .:/app  # 注释掉开发时的volume挂载，使用容器内构建的代码

networks:
  inkflow-network:
    driver: bridge
  database-network:
    external: true
    name: database-network