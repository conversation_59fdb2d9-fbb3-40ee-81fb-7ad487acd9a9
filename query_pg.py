#!/usr/bin/env python3
"""
查询PostgreSQL数据库中的小说内容
"""
import psycopg2
import sys
from psycopg2.extras import RealDictCursor

def get_db_connection():
    """获取数据库连接"""
    database_url = 'postgresql://admin:admin@localhost:5432/ai_novel'
    
    try:
        conn = psycopg2.connect(database_url)
        return conn
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return None

def list_all_stories():
    """列出所有故事"""
    conn = get_db_connection()
    if not conn:
        return None
    
    try:
        cursor = conn.cursor(cursor_factory=RealDictCursor)
        
        cursor.execute("""
            SELECT s.id, s.title, s.style, s.status, s.current_chapter_number, 
                   s.created_at, COUNT(c.id) as actual_chapters
            FROM stories s 
            LEFT JOIN chapters c ON s.id = c.story_id
            GROUP BY s.id, s.title, s.style, s.status, s.current_chapter_number, s.created_at
            ORDER BY actual_chapters DESC, s.created_at DESC
        """)
        
        stories = [dict(row) for row in cursor.fetchall()]
        conn.close()
        
        print("📚 PostgreSQL数据库中的所有故事:")
        print("=" * 80)
        
        if not stories:
            print("❌ 数据库中没有故事")
            return None
        
        for i, story in enumerate(stories, 1):
            print(f"{i}. ID: {story['id']}")
            print(f"   标题: {story['title']}")
            print(f"   风格: {story['style']}")
            print(f"   记录章节数: {story['current_chapter_number']}")
            print(f"   实际章节数: {story['actual_chapters']}")
            print(f"   创建时间: {story['created_at']}")
            print("-" * 40)
        
        return stories[0]['id'] if stories else None
        
    except Exception as e:
        conn.close()
        print(f"❌ 查询失败: {str(e)}")
        return None

def query_story_detail(story_id: str):
    """查询故事详细信息"""
    conn = get_db_connection()
    if not conn:
        return
    
    try:
        cursor = conn.cursor(cursor_factory=RealDictCursor)
        
        # 查询故事基本信息
        cursor.execute("""
            SELECT id, title, style, status, current_chapter_number, 
                   created_at, updated_at
            FROM stories 
            WHERE id = %s
        """, (story_id,))
        
        story = cursor.fetchone()
        if not story:
            print(f"❌ 未找到ID为 {story_id} 的故事")
            return
        
        print("=" * 80)
        print(f"📚 故事详细分析: {story['title']}")
        print("=" * 80)
        print(f"ID: {story['id']}")
        print(f"标题: {story['title']}")
        print(f"风格: {story['style']}")
        print(f"状态: {story['status']}")
        print(f"当前章节数: {story['current_chapter_number']}")
        print(f"创建时间: {story['created_at']}")
        
        # 查询所有章节
        cursor.execute("""
            SELECT id, chapter_number, title, content, summary, created_at
            FROM chapters 
            WHERE story_id = %s 
            ORDER BY chapter_number
        """, (story_id,))
        
        chapters = cursor.fetchall()
        
        print(f"\n📝 章节分析 (共{len(chapters)}章):")
        
        total_words = 0
        for chapter in chapters:
            print(f"\n--- 第{chapter['chapter_number']}章: {chapter['title']} ---")
            
            content_length = len(chapter['content'])
            total_words += content_length
            print(f"   📏 内容长度: {content_length} 字")
            
            if chapter['summary']:
                print(f"   📋 摘要: {chapter['summary'][:150]}...")
            
            # 内容预览
            content_preview = chapter['content'][:300].replace('\n', ' ')
            print(f"   👀 内容预览: {content_preview}...")
            
            # 查询该章节的选择选项
            cursor.execute("""
                SELECT id, choice_text, choice_type, is_selected, created_at
                FROM choices 
                WHERE chapter_id = %s
                ORDER BY created_at
            """, (chapter['id'],))
            
            choices = cursor.fetchall()
            print(f"   🎯 选择选项 ({len(choices)}个):")
            for j, choice in enumerate(choices, 1):
                selected_mark = "✅" if choice['is_selected'] else "⭕"
                print(f"      {j}. {selected_mark} {choice['choice_text']}")
        
        # 整体质量分析
        if chapters:
            avg_words = total_words / len(chapters)
            word_counts = [len(ch['content']) for ch in chapters]
            min_words = min(word_counts)
            max_words = max(word_counts)
            
            print(f"\n📊 整体质量分析:")
            print(f"   📏 总字数: {total_words} 字")
            print(f"   📏 平均章节长度: {avg_words:.0f} 字")
            print(f"   📏 章节长度范围: {min_words} - {max_words} 字")
            
            # 分析章节间关联性
            print(f"\n🔗 章节关联性分析:")
            for i in range(1, len(chapters)):
                current_chapter = chapters[i]
                prev_chapter = chapters[i-1]
                
                # 简单的关联性检查
                current_words = set(current_chapter['content'][:500].split())
                prev_words = set(prev_chapter['content'][-500:].split())
                common_words = current_words.intersection(prev_words)
                common_words = {w for w in common_words if len(w) > 2}
                
                print(f"   第{prev_chapter['chapter_number']}章 → 第{current_chapter['chapter_number']}章: {len(common_words)}个关联词")
                if len(common_words) < 5:
                    print(f"      ⚠️ 关联性较弱，可能存在连贯性问题")
                
        conn.close()
        
    except Exception as e:
        conn.close()
        print(f"❌ 查询失败: {str(e)}")

if __name__ == "__main__":
    if len(sys.argv) > 1:
        if sys.argv[1] == "--list":
            list_all_stories()
        else:
            story_id = sys.argv[1]
            print(f"🔍 正在查询故事ID: {story_id}")
            query_story_detail(story_id)
    else:
        # 先列出所有故事
        first_story_id = list_all_stories()
        
        if first_story_id:
            print(f"\n🔍 正在分析第一个故事: {first_story_id}")
            query_story_detail(first_story_id)
