"""simplify_worldview_structure

Revision ID: 4f318695c4da
Revises: 
Create Date: 2025-07-29 10:32:36.596571

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '4f318695c4da'
down_revision: Union[str, Sequence[str], None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # 1. 添加新字段
    op.add_column('worldviews', sa.Column('worldview_data', sa.JSON(), nullable=True))
    op.add_column('worldviews', sa.Column('summary', sa.Text(), nullable=True))

    # 2. 迁移现有数据到新的JSON结构
    op.execute("""
        UPDATE worldviews SET
        worldview_data = json_build_object(
            'world_setting', COALESCE(world_setting, ''),
            'power_system', COALESCE(power_system, ''),
            'social_structure', COALESCE(social_structure, ''),
            'geography', COALESCE(geography, ''),
            'history_background', COALESCE(history_background, ''),
            'main_character', COALESCE(main_character, '{}'),
            'supporting_characters', COALESCE(supporting_characters, '[]'),
            'antagonists', COALESCE(antagonists, '[]'),
            'main_plot', COALESCE(main_plot, ''),
            'conflict_setup', COALESCE(conflict_setup, ''),
            'story_themes', COALESCE(story_themes, '[]'),
            'narrative_style', COALESCE(narrative_style, ''),
            'tone_atmosphere', COALESCE(tone_atmosphere, '')
        ),
        summary = COALESCE(world_setting, '未知世界')
    """)

    # 3. 设置worldview_data为非空
    op.alter_column('worldviews', 'worldview_data', nullable=False)

    # 4. 删除旧字段
    op.drop_column('worldviews', 'tone_atmosphere')
    op.drop_column('worldviews', 'power_system')
    op.drop_column('worldviews', 'narrative_style')
    op.drop_column('worldviews', 'world_setting')
    op.drop_column('worldviews', 'social_structure')
    op.drop_column('worldviews', 'history_background')
    op.drop_column('worldviews', 'supporting_characters')
    op.drop_column('worldviews', 'conflict_setup')
    op.drop_column('worldviews', 'main_plot')
    op.drop_column('worldviews', 'geography')
    op.drop_column('worldviews', 'story_themes')
    op.drop_column('worldviews', 'antagonists')
    op.drop_column('worldviews', 'main_character')


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('worldviews', sa.Column('main_character', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True))
    op.add_column('worldviews', sa.Column('antagonists', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True))
    op.add_column('worldviews', sa.Column('story_themes', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True))
    op.add_column('worldviews', sa.Column('geography', sa.TEXT(), autoincrement=False, nullable=True))
    op.add_column('worldviews', sa.Column('main_plot', sa.TEXT(), autoincrement=False, nullable=True))
    op.add_column('worldviews', sa.Column('conflict_setup', sa.TEXT(), autoincrement=False, nullable=True))
    op.add_column('worldviews', sa.Column('supporting_characters', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True))
    op.add_column('worldviews', sa.Column('history_background', sa.TEXT(), autoincrement=False, nullable=True))
    op.add_column('worldviews', sa.Column('social_structure', sa.TEXT(), autoincrement=False, nullable=True))
    op.add_column('worldviews', sa.Column('world_setting', sa.TEXT(), autoincrement=False, nullable=False))
    op.add_column('worldviews', sa.Column('narrative_style', sa.TEXT(), autoincrement=False, nullable=True))
    op.add_column('worldviews', sa.Column('power_system', sa.TEXT(), autoincrement=False, nullable=True))
    op.add_column('worldviews', sa.Column('tone_atmosphere', sa.TEXT(), autoincrement=False, nullable=True))
    op.drop_column('worldviews', 'summary')
    op.drop_column('worldviews', 'worldview_data')
    # ### end Alembic commands ###
