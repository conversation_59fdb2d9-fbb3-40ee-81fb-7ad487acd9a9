# Node modules
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build outputs
dist
build

# Environment files (keep .env for Docker)
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE files
.vscode
.idea
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Git
.git
.gitignore

# Documentation (但保留README.md用于构建)
docs/
*.md
!README.md

# Test files
**/*.test.js
**/*.test.ts
**/*.test.tsx
**/*.spec.js
**/*.spec.ts
**/*.spec.tsx

# Coverage
coverage

# Logs
logs
*.log

# Python specific
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
.pytest_cache/
.coverage
htmlcov/
.tox/
.cache
.venv/
venv/
env/

# Test files (Python)
test_*.py
*_test.py
tests/

# Scripts (development only)
scripts/

# Docker files
Dockerfile*
docker-compose*
.dockerignore