# 优化后的AI提示词集合

## 📋 概述

本文档包含了AI小说质量优化方案中的所有提示词模板，按功能分类整理。

## 🎭 角色档案生成提示词

### CHARACTER_PROFILE_PROMPT

```python
CHARACTER_PROFILE_PROMPT = """
基于以下世界观信息，为主角创建详细的角色档案：

世界观信息：
世界设定：{world_setting}
力量体系：{power_system}
主角描述：{main_character}
故事风格：{style}
故事主题：{story_themes}

请按以下格式输出角色档案：

角色姓名：[具体的中文姓名，符合{style}风格]
性格特点：[3-5个核心性格特征，用逗号分隔]
说话方式：[描述角色的语言风格和表达习惯]
背景设定：[角色的出身、经历、当前处境]
外貌特征：[简洁的外貌描述，突出特色]
核心能力：[角色的主要能力或天赋]
成长目标：[角色想要达成的目标]
关键关系：[与其他重要角色的关系]

**角色设计要求：**
1. **姓名要求**：
   - 必须是具体的中文姓名，不能使用"主角"、"我"等代称
   - 符合{style}风格的命名习惯
   - 朗朗上口，便于记忆

2. **性格设计**：
   - 要有明显的优点和缺点，避免完美人设
   - 性格特点要能产生戏剧冲突
   - 与背景设定高度契合

3. **背景设定**：
   - 必须与世界观设定完全吻合
   - 要有足够的深度支撑长篇故事
   - 包含成长空间和发展潜力

4. **说话方式**：
   - 要有明显的个人特色
   - 符合角色的出身和性格
   - 便于在对话中识别角色

5. **成长目标**：
   - 要能支撑整个故事的发展
   - 与世界观的核心冲突相关
   - 有明确的阶段性目标

**示例输出：**
角色姓名：林浩
性格特点：倔强不屈，正义感强，容易冲动，聪明机敏，重情重义
说话方式：简洁直接，不喜废话，语气坚定，偶尔带有少年意气
背景设定：原太虚宗外门弟子，因天赋异禀遭同门嫉妒，被诬陷偷盗宗门秘籍而逐出师门
外貌特征：剑眉星目，身材修长，眼神坚毅，左手腕有一道神秘印记
核心能力：拥有罕见的星辰血脉，能够感知和操控星辰之力
成长目标：证明自己的清白，重回宗门，最终成为守护苍生的强者
关键关系：与师父关系复杂（既有师恩又有被逐之恨），与师兄弟关系微妙
"""
```

## 📖 优化后的章节生成提示词

### ENHANCED_CHAPTER_PROMPT

```python
ENHANCED_CHAPTER_PROMPT = """
{style_prompt}

{context}

**🎭 角色一致性要求（必须严格执行）：**
- 主角姓名：{character_name}
- 必须使用第三人称叙述（"他"、"{character_name}"、"这位年轻人"等）
- 严禁使用第一人称（"我"、"我的"、"我们"等）
- 角色性格特点：{personality_traits}
- 说话风格：{speaking_style}
- 角色行为必须符合既定性格，不能出现性格分裂

**🔗 强制性衔接要求（必须严格执行）：**
1. **开头衔接**：本章开头必须直接承接上章结尾场景，不得重新开始故事
   - 如果上章结尾{character_name}在特定位置，本章必须从该位置继续
   - 如果上章结尾有未完成的动作，本章必须继续该动作
   - 禁止重复描述已知的背景信息

2. **选择后果体现**：如果用户做出了选择，本章第一段必须立即展现选择的直接结果
   - 用户选择必须在开头3句话内得到明确体现
   - 选择的后果要符合逻辑，不能敷衍了事

3. **状态连续性**：{character_name}的位置、状态、情绪必须与上章结尾保持连续
   - 不能突然改变{character_name}的位置而不解释
   - {character_name}的体力、法力、情绪状态要有合理的延续
   - 环境状态要保持一致性

**📝 输出格式要求（必须严格遵守）：**
首先输出章节标题，格式为：
CHAPTER_TITLE: 具体的章节标题（如：血祭危机、乾坤印觉醒、绝境逃生等）

然后输出章节正文内容，直接从故事开始，不要包含任何格式化标记。

**📚 内容要求（必须遵守）：**
1. **字数要求**：章节内容必须在{min_words}-{max_words}字之间，这是硬性要求！
2. **内容质量**：必须是完整的故事章节，包含详细的情节描述、人物对话、环境描写
3. **故事连贯**：严格按照上述衔接要求，确保与前章无缝连接
4. **角色塑造**：通过行动、对话、心理活动展现{character_name}的性格特点
5. **结构完整**：有明确的开头、发展、高潮、结尾
6. **悬念设置**：章节结尾要为下一个选择做铺垫，但要明确描述结尾状态
7. **标题要求**：章节标题要简洁有力，体现本章核心情节，不超过8个字

**🎨 写作技巧要求：**
1. **节奏控制**：合理安排情节节奏，张弛有度
2. **人物塑造**：通过行动、对话、心理活动展现人物性格
3. **环境描写**：适当的环境描写增强代入感
4. **冲突设置**：每章都要有明确的冲突或转折点
5. **情感渲染**：通过细节描写增强情感共鸣
6. **对话真实**：{character_name}的对话要符合其说话风格：{speaking_style}
7. **行为一致**：{character_name}的行为要体现其性格：{personality_traits}

**❌ 严格禁止的行为：**
- 使用第一人称叙述（"我"、"我的"等）
- 重新开始故事或重复背景介绍
- 忽略用户选择的影响
- 突然改变{character_name}位置而不解释
- 与上章情节出现逻辑矛盾
- 角色行为与既定性格不符
- 模糊的结尾描述

**重要提醒**：
- 请确保生成足够长的内容，至少{min_words}字！不要过早结束！
- 章节标题要放在最开头，格式：CHAPTER_TITLE: 标题内容
- 正文内容不要包含"第X章"、"**第X章：标题**"等格式化标记
- 直接从故事正文开始，立即承接上章结尾
- 始终记住主角的名字是{character_name}，不是"我"

请基于以上要求创作下一章节内容。
"""
```

## 📋 优化后的摘要生成提示词

### ENHANCED_SUMMARY_PROMPT

```python
ENHANCED_SUMMARY_PROMPT = """
请阅读以下完整章节内容，并生成详细的章节摘要：

章节内容：
{chapter_content}

**摘要格式要求（必须严格遵守）：**
摘要必须包含两个核心维度的信息：

**1. 主角信息维度：**
- 主角的精确位置（具体在哪里，不能模糊）
- 主角的身体状态（健康、疲劳、装备、伤势等）
- 主角的心理状态（情绪、想法、决心等）
- 主角当前拥有的能力/物品/工具
- 主角与其他角色的关系变化

**2. 故事发展维度：**
- 本章发生的主要事件和关键转折点
- 当前正在进行中的事件（未完成的动作或计划）
- 新发现的重要线索或信息
- 当前面临的主要冲突或挑战
- 环境或世界状态的重要变化

**具体要求：**
1. **字数控制**：摘要长度控制在300-350字之间
2. **信息完整**：必须涵盖上述两个维度的所有要点
3. **精确描述**：避免使用"某处"、"不久"等模糊词汇
4. **衔接导向**：重点描述章节结尾状态，为下章提供明确起点
5. **自然语言**：使用流畅的自然语言，不要使用列表格式
6. **角色姓名**：必须使用角色的具体姓名，不能使用"主角"、"我"等代称

**示例格式：**
"[主要情节描述，包含关键事件和转折点]。在这个过程中，[主角的行动和决策]，[重要发现或线索]。章节结尾时，[主角姓名]位于[精确位置描述]，[身体状态描述]，[心理状态描述]，[手中物品或装备]。当前[环境状态和氛围]，[正在进行的事件或即将面临的挑战]，[为下章设置的具体悬念或选择点]。"

**重要提醒：**
- 摘要是下一章生成的重要依据，必须提供足够详细的状态信息
- 特别注意描述主角的确切位置和状态，避免下章出现位置跳跃
- 确保包含未完成的事件，让下章能够自然承接
- 必须使用主角的具体姓名，建立角色一致性
"""
```

## 🌍 上下文构建模板

### ENHANCED_CONTEXT_TEMPLATE

```python
ENHANCED_CONTEXT_TEMPLATE = """
**故事背景信息：**
- 故事标题：{title}
- 故事风格：{style}
- 当前章节：第{chapter_number}章

**🎭 角色信息：**
- 主角姓名：{character_name}
- 性格特点：{personality_traits}
- 说话风格：{speaking_style}
- 当前状态：{character_current_state}

**🌍 世界观设定：**
{worldview}

**📚 故事发展脉络：**
{story_summary}

**🎯 用户选择影响：**
{user_choice_context}

**📍 当前情境：**
{current_situation}
"""
```

## 🔧 使用说明

### 1. 角色档案生成流程
1. 在创建世界观时调用 `CHARACTER_PROFILE_PROMPT`
2. 解析AI返回的角色信息
3. 保存到 `character_profiles` 表
4. 在后续章节生成中使用角色信息

### 2. 章节生成流程
1. 获取角色档案信息
2. 构建增强的上下文
3. 使用 `ENHANCED_CHAPTER_PROMPT` 生成章节
4. 验证生成内容的人称使用
5. 使用 `ENHANCED_SUMMARY_PROMPT` 生成摘要

### 3. 质量检查要点
- 检查是否使用了正确的角色姓名
- 验证没有第一人称的使用
- 确认章节衔接的连贯性
- 验证角色行为的一致性

---
*文档版本：v1.0*  
*创建时间：2025-08-01*  
*最后更新：2025-08-01*
