# LLM抽象层开发 TodoList

## 项目概述
- **目标**：将现有的单一Gemini集成重构为支持多LLM的抽象层
- **核心技术**：LlamaIndex + OpenRouter + 结构化输出
- **预期收益**：成本优化、技术灵活性、结构化输出替代Prompt约定

## 开发计划总览

### 🎯 当前任务
- [x] **已完成**: LLM抽象层基础架构设计和实现 (预估: 10小时) ✅
  - **开始时间**: 2025-01-27
  - **完成时间**: 2025-01-27
  - **负责人**: 结构化编程专家
  - **状态**: ✅ 已完成

- [ ] **正在进行**: 集成 LlamaIndex + OpenRouter (预估: 6小时)
  - **开始时间**: 2025-01-27
  - **负责人**: 结构化编程专家
  - **状态**: 🔄 进行中

### 📋 待办任务
- [ ] 实现结构化输出重构 (预估: 8小时)
- [ ] 多模型配置和智能选择 (预估: 4小时)
- [ ] 成本监控和使用统计 (预估: 4小时)
- [ ] 测试和性能对比 (预估: 6小时)
- [ ] 原有测试体系建立 (预估: 8小时)

### ✅ 已完成
- [x] OpenRouter 和 LlamaIndex 技术调研
- [x] 架构方案设计
- [x] 开发计划制定
- [x] LLM抽象层基础架构设计和实现
- [x] 依赖包安装和配置
- [x] 抽象层接口设计
- [x] 结构化输出模型定义
- [x] 结构化AI服务实现

---

## Phase 1: LLM抽象层基础架构 (当前阶段)

### 1.1 依赖包安装和配置
- [x] **安装LlamaIndex核心包** (预估: 0.5小时) ✅ 已完成
  - [x] 安装 `llama-index-core`
  - [x] 安装 `llama-index-llms-openrouter`
  - [x] 安装 `llama-index-llms-gemini`
  - [x] 更新 `pyproject.toml` 依赖

- [x] **环境配置** (预估: 0.5小时) ✅ 已完成
  - [x] 添加 OpenRouter API Key 配置
  - [x] 更新 `settings.py` 配置文件
  - [x] 创建 `.env.example` 示例

### 1.2 抽象层接口设计
- [x] **创建LLM提供商抽象基类** (预估: 2小时) ✅ 已完成
  - [x] 设计 `LLMProvider` 抽象基类
  - [x] 定义流式生成接口
  - [x] 定义结构化输出接口
  - [x] 定义错误处理接口

- [x] **创建具体实现类** (预估: 3小时) ✅ 已完成
  - [x] 实现 `LlamaIndexProvider` 类
  - [x] 实现 `GeminiProvider` 类（兼容现有）
  - [x] 实现 `OpenRouterProvider` 类
  - [x] 实现提供商工厂模式

### 1.3 结构化输出模型定义
- [x] **定义Pydantic输出模型** (预估: 2小时) ✅ 已完成
  - [x] 创建 `ChapterContent` 模型
  - [x] 创建 `ChoiceOption` 模型
  - [x] 创建 `ChapterResponse` 模型
  - [x] 创建 `WorldViewResponse` 模型

- [x] **创建结构化输出服务** (预估: 2小时) ✅ 已完成
  - [x] 实现 `StructuredAIService` 类
  - [x] 集成 `FunctionCallingProgram`
  - [x] 实现Prompt模板管理
  - [x] 实现输出验证和错误处理

---

## Phase 2: LlamaIndex + OpenRouter 集成

### 2.1 OpenRouter集成
- [ ] **OpenRouter客户端配置** (预估: 2小时)
  - [ ] 配置OpenRouter LLM实例
  - [ ] 实现模型列表获取
  - [ ] 实现模型状态检查
  - [ ] 配置请求头和认证

- [ ] **免费模型配置** (预估: 2小时)
  - [ ] 配置 DeepSeek V3 模型
  - [ ] 配置 Kimi K2 模型
  - [ ] 实现模型可用性检测
  - [ ] 配置模型参数优化

### 2.2 LlamaIndex集成
- [ ] **LlamaIndex服务层** (预估: 2小时)
  - [ ] 创建 `LlamaIndexService` 类
  - [ ] 实现流式生成包装
  - [ ] 实现异步调用支持
  - [ ] 集成错误重试机制

---

## Phase 3: 结构化输出重构

### 3.1 章节生成重构
- [ ] **重构章节生成逻辑** (预估: 4小时)
  - [ ] 替换现有Prompt约定为结构化输出
  - [ ] 重构 `generate_first_chapter_stream`
  - [ ] 重构 `generate_next_chapter_stream`
  - [ ] 保持流式输出体验

- [ ] **选择生成重构** (预估: 2小时)
  - [ ] 重构 `generate_choices` 方法
  - [ ] 实现结构化选择输出
  - [ ] 优化选择差异度算法

### 3.2 世界观生成重构
- [ ] **世界观服务重构** (预估: 2小时)
  - [ ] 重构 `WorldViewService`
  - [ ] 实现结构化世界观输出
  - [ ] 优化世界观一致性检查

---

## Phase 4: 多模型配置和智能选择

### 4.1 模型配置管理
- [ ] **创建模型配置系统** (预估: 2小时)
  - [ ] 定义 `LLMModel` 枚举
  - [ ] 创建 `LLMConfig` 配置类
  - [ ] 实现模型优先级配置
  - [ ] 支持动态配置更新

### 4.2 智能模型选择
- [ ] **实现智能选择逻辑** (预估: 2小时)
  - [ ] 创建 `SmartLLMService` 类
  - [ ] 实现基于成本的模型选择
  - [ ] 实现基于任务类型的模型选择
  - [ ] 实现故障转移机制

---

## Phase 5: 成本监控和使用统计

### 5.1 使用统计
- [ ] **创建使用跟踪系统** (预估: 2小时)
  - [ ] 实现 `UsageTracker` 类
  - [ ] 记录API调用次数和成本
  - [ ] 实现使用量统计报告
  - [ ] 集成Redis缓存统计

### 5.2 成本监控
- [ ] **实现成本控制** (预估: 2小时)
  - [ ] 设置成本阈值告警
  - [ ] 实现自动降级到免费模型
  - [ ] 创建成本分析报告
  - [ ] 实现预算控制机制

---

## Phase 6: 测试和性能对比

### 6.1 功能测试
- [ ] **创建LLM抽象层测试** (预估: 3小时)
  - [ ] 单元测试：提供商接口测试
  - [ ] 集成测试：OpenRouter集成测试
  - [ ] 功能测试：结构化输出测试
  - [ ] 性能测试：流式生成测试

### 6.2 性能对比
- [ ] **模型性能评估** (预估: 3小时)
  - [ ] 对比不同模型的生成质量
  - [ ] 测试响应时间和稳定性
  - [ ] 评估成本效益比
  - [ ] 生成性能报告

---

## Phase 7: 原有测试体系建立

### 7.1 测试基础设施
- [ ] **创建测试目录结构** (预估: 1小时)
  - [ ] 创建 `tests/` 目录
  - [ ] 配置pytest环境
  - [ ] 创建测试配置文件
  - [ ] 设置测试数据库

### 7.2 核心功能测试
- [ ] **StoryService测试** (预估: 3小时)
  - [ ] 测试故事创建和管理
  - [ ] 测试章节生成流程
  - [ ] 测试选择处理逻辑
  - [ ] 测试世界观集成

### 7.3 API端点测试
- [ ] **API集成测试** (预估: 4小时)
  - [ ] 测试认证端点
  - [ ] 测试故事管理API
  - [ ] 测试章节生成API
  - [ ] 测试流式响应

---

## 文件结构规划

```
app/
├── services/
│   ├── llm/
│   │   ├── __init__.py
│   │   ├── base.py              # LLM抽象基类
│   │   ├── providers/
│   │   │   ├── __init__.py
│   │   │   ├── llamaindex_provider.py
│   │   │   ├── gemini_provider.py
│   │   │   └── openrouter_provider.py
│   │   ├── models/
│   │   │   ├── __init__.py
│   │   │   ├── chapter_models.py
│   │   │   └── worldview_models.py
│   │   ├── structured_ai_service.py
│   │   ├── smart_llm_service.py
│   │   └── usage_tracker.py
│   ├── ai_service.py            # 重构后的AI服务
│   └── story_service.py         # 更新的故事服务
├── config/
│   ├── llm_config.py           # LLM配置管理
│   └── settings.py             # 更新的设置
└── models/
    └── llm_responses.py        # 结构化响应模型

tests/
├── unit/
│   ├── test_llm_providers.py
│   ├── test_structured_output.py
│   └── test_smart_selection.py
├── integration/
│   ├── test_openrouter_integration.py
│   ├── test_story_service.py
│   └── test_api_endpoints.py
└── performance/
    └── test_model_comparison.py
```

---

## 风险控制和注意事项

### 🚨 关键风险
1. **API兼容性**：确保新抽象层与现有API完全兼容
2. **性能影响**：抽象层不能显著影响响应速度
3. **成本控制**：避免意外的高额API调用费用
4. **数据一致性**：确保结构化输出与现有数据格式兼容

### 🛡️ 风险缓解
- **渐进式迁移**：保留原有实现作为fallback
- **全面测试**：每个阶段都进行充分测试
- **监控告警**：实时监控API调用和成本
- **回滚机制**：准备快速回滚到原有实现

---

## 成功指标

### 📊 技术指标
- [ ] API响应时间不超过原有实现的110%
- [ ] 结构化输出成功率 > 95%
- [ ] 测试覆盖率 > 80%
- [ ] 零停机迁移完成

### 💰 成本指标
- [ ] 整体API调用成本降低 > 30%
- [ ] 免费模型使用率 > 60%
- [ ] 成本监控准确率 > 99%

### 🎯 功能指标
- [ ] 支持至少3个不同LLM提供商
- [ ] 智能模型选择准确率 > 90%
- [ ] 故障转移成功率 > 95%

---

## 更新日志

### 2025-01-27
- [x] 创建开发TodoList文档
- [x] 完成技术调研和方案设计
- [x] 完成Phase 1: LLM抽象层基础架构开发
  - [x] 依赖包安装和配置
  - [x] 抽象层接口设计
  - [x] 结构化输出模型定义
  - [x] 结构化AI服务实现
- [x] 开始Phase 2: LlamaIndex + OpenRouter 集成

---

**下一步行动**: 继续实施 Phase 2 - OpenRouter集成和测试
