# 快速实施指南

## 🚀 第一阶段：角色一致性系统（预计3-5天）

### Day 1: 数据库准备

**使用提供的SQL脚本快速初始化：**

```bash
# 方式1：直接执行SQL脚本文件
psql -h localhost -p 5432 -U admin -d ai_novel -f sql/character_profiles_setup.sql

# 方式2：通过Docker执行
docker exec -i postgres-server psql -U admin -d ai_novel < sql/character_profiles_setup.sql

# 方式3：手动连接后执行
docker exec -it postgres-server psql -U admin -d ai_novel
# 然后在psql中执行：\i /path/to/character_profiles_setup.sql
```

**脚本包含的内容：**
- ✅ 创建 `character_profiles` 表
- ✅ 创建必要的索引（4个）
- ✅ 为 `worldviews` 表添加 `protagonist_name` 字段
- ✅ 创建便于查询的视图
- ✅ 设置自动更新时间戳的触发器
- ✅ 权限配置和验证检查

**验证安装：**
```sql
-- 检查表是否创建成功
SELECT table_name FROM information_schema.tables
WHERE table_name = 'character_profiles';

-- 检查字段是否添加成功
SELECT column_name FROM information_schema.columns
WHERE table_name = 'worldviews' AND column_name = 'protagonist_name';
```

### Day 2: 服务层实现
**创建文件：** `app/services/character_service.py`

**核心方法：**
- `create_protagonist_profile()` - 创建主角档案
- `get_character_context()` - 获取角色上下文
- `update_character_state()` - 更新角色状态

**修改文件：** `app/services/worldview_service.py`
- 在创建世界观时同时创建角色档案

### Day 3: AI服务集成
**修改文件：** `app/services/ai_service.py`
- 在 `generate_chapter_stream()` 中集成角色信息
- 修改上下文构建逻辑

**修改文件：** `app/prompts/unified_prompts.py`
- 替换章节生成提示词为 `ENHANCED_CHAPTER_PROMPT`
- 添加角色档案生成提示词

### Day 4: API接口
**创建文件：** `app/api/characters.py`
- 角色档案查询接口
- 角色状态更新接口

**修改文件：** `app/api/stories.py`
- 世界观创建接口返回角色信息

### Day 5: 测试验证
**创建测试：**
- 角色档案CRUD测试
- 章节生成人称检测测试
- 端到端流程测试

## 📋 关键代码片段

### 1. 角色服务核心实现
```python
# app/services/character_service.py
class CharacterService:
    def __init__(self):
        self.ai_service = AIService()
    
    def create_protagonist_profile(self, story_id: str, worldview_data: dict):
        # 生成角色档案
        prompt = self._build_character_prompt(worldview_data)
        ai_response = self.ai_service.generate_text(prompt)
        
        # 解析并保存
        character_data = self._parse_character_data(ai_response)
        return self._save_character_profile(story_id, character_data)
    
    def get_character_context(self, story_id: str):
        character = self._get_protagonist(story_id)
        return {
            "character_name": character.character_name,
            "personality_traits": character.personality_traits,
            "speaking_style": character.speaking_style,
            "current_state": character.current_state or "正常状态"
        }
```

### 2. 修改章节生成
```python
# app/services/ai_service.py
def generate_chapter_stream(self, story_id: str, user_choice: str = None):
    # 获取角色上下文
    character_context = self.character_service.get_character_context(story_id)
    
    # 构建提示词
    prompt = self.prompt_manager.get_enhanced_chapter_prompt(
        context=context,
        character_name=character_context["character_name"],
        personality_traits=character_context["personality_traits"],
        speaking_style=character_context["speaking_style"]
    )
    
    # 生成章节
    for chunk in self.llm_provider.generate_stream(prompt):
        yield chunk
```

### 3. 人称检测函数
```python
# app/utils/content_validator.py
import re

def validate_third_person_narration(content: str, character_name: str) -> dict:
    """验证第三人称叙述"""
    
    # 检测第一人称
    first_person_patterns = [r'\b我\b', r'\b我的\b', r'\b我们\b']
    first_person_violations = []
    
    for pattern in first_person_patterns:
        matches = re.findall(pattern, content)
        first_person_violations.extend(matches)
    
    # 检测角色姓名使用
    name_count = content.count(character_name)
    
    return {
        "is_valid": len(first_person_violations) == 0,
        "first_person_violations": first_person_violations,
        "character_name_count": name_count,
        "recommendations": _generate_recommendations(first_person_violations, name_count)
    }
```

## ✅ 验证清单

### 功能验证
- [ ] 创建新故事时自动生成角色档案
- [ ] 角色档案包含具体姓名（非"我"或"主角"）
- [ ] 新生成的章节使用第三人称叙述
- [ ] 角色对话符合设定的说话风格
- [ ] 角色行为符合性格特点

### 技术验证
- [ ] 数据库表创建成功
- [ ] API接口正常工作
- [ ] 单元测试通过
- [ ] 集成测试通过
- [ ] 性能无明显下降

### 内容质量验证
- [ ] 生成的角色姓名符合故事风格
- [ ] 章节中角色姓名出现频率 ≥ 3次
- [ ] 无第一人称使用
- [ ] 角色性格一致性良好

## 🔧 故障排除

### 常见问题

**1. 角色档案生成失败**
- 检查AI服务是否正常
- 验证提示词格式是否正确
- 检查世界观数据是否完整

**2. 章节仍使用第一人称**
- 确认提示词已更新
- 检查角色上下文是否正确传递
- 验证AI服务是否使用了新提示词

**3. 数据库表创建失败**
- 检查PostgreSQL连接是否正常
- 确认有足够的数据库权限
- 验证stories表是否存在（外键依赖）
- 检查UUID扩展是否已启用：`CREATE EXTENSION IF NOT EXISTS "uuid-ossp";`

### 调试技巧
```python
# 添加调试日志
logger.info(f"角色上下文: {character_context}")
logger.info(f"生成的提示词长度: {len(prompt)}")
logger.info(f"章节内容预览: {content[:200]}")
```

## 📊 成功指标

### 第一阶段目标
- 新故事100%生成角色档案
- 新章节100%使用第三人称
- 角色姓名在章节中出现 ≥ 3次
- 用户反馈阅读体验改善

### 监控方法
```python
# 内容质量监控
def monitor_content_quality(chapter_content: str, character_name: str):
    validation_result = validate_third_person_narration(chapter_content, character_name)
    
    # 记录指标
    metrics = {
        "third_person_compliance": validation_result["is_valid"],
        "character_name_frequency": validation_result["character_name_count"],
        "content_length": len(chapter_content)
    }
    
    # 发送到监控系统
    send_metrics(metrics)
```

## 🎯 下一步计划

### 第二阶段准备（Week 2）
- 基于角色档案优化章节衔接
- 实现更精确的状态传递
- 强化选择影响机制

### 长期优化方向
- 多角色管理系统
- 角色关系动态追踪
- 智能情节分支系统

---
*快速实施指南 v1.0*  
*预计实施时间：3-5天*  
*风险等级：低*
