# InkFlow AI - 生产部署指南

## 🎯 概述

本文档介绍如何在生产环境中部署InkFlow AI应用。

## 📋 系统要求

### 硬件要求
- **CPU**: 2核心以上
- **内存**: 4GB以上
- **存储**: 20GB以上可用空间
- **网络**: 稳定的互联网连接

### 软件要求
- **操作系统**: Linux (推荐Ubuntu 20.04+)
- **Docker**: 20.10+
- **Docker Compose**: 2.0+

## 🔧 环境准备

### 1. 安装Docker

```bash
# Ubuntu/Debian
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# 启动Docker服务
sudo systemctl start docker
sudo systemctl enable docker
```

### 2. 安装Docker Compose

```bash
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

### 3. 创建数据库网络

InkFlow AI依赖外部数据库服务，需要先创建数据库网络：

```bash
# 创建数据库网络
docker network create database-network

# 启动PostgreSQL (如果没有现有数据库)
docker run -d \
  --name postgres-server \
  --network database-network \
  -e POSTGRES_USER=admin \
  -e POSTGRES_PASSWORD=admin \
  -e POSTGRES_DB=ai_novel \
  -v postgres_data:/var/lib/postgresql/data \
  postgres:15

# 启动Redis (如果没有现有Redis)
docker run -d \
  --name redis-server \
  --network database-network \
  -v redis_data:/data \
  redis:7-alpine
```

## 🚀 部署步骤

### 1. 获取代码

```bash
git clone <your-repository-url>
cd inkflow-ai
```

### 2. 配置环境变量

复制并编辑环境配置文件：

```bash
cp .env.example .env
```

编辑 `.env` 文件，配置以下关键参数：

```bash
# 应用配置
APP_NAME=InkFlow AI
APP_VERSION=1.0.0
DEBUG=false

# 数据库配置
DATABASE_URL=*********************************************/ai_novel

# Redis配置
REDIS_URL=redis://redis-server:6379/0

# AI模型配置
GEMINI_API_KEY=your_gemini_api_key_here
SILICONFLOW_API_KEY=your_siliconflow_api_key_here

# LLM配置
DEFAULT_LLM_PROVIDER=siliconflow

# 安全配置
SECRET_KEY=your-super-secret-key-here
```

### 3. 部署应用

使用部署脚本：

```bash
# 方式1: 使用部署脚本
./scripts/deploy.sh

# 方式2: 使用管理脚本
./scripts/manage.sh deploy

# 方式3: 使用Makefile
make deploy
```

### 4. 验证部署

```bash
# 检查服务状态
make status

# 检查健康状态
make health

# 查看日志
make logs
```

## 📊 服务管理

### 常用命令

```bash
# 启动服务
make start

# 停止服务
make stop

# 重启服务
make restart

# 查看状态
make status

# 查看日志
make logs

# 检查健康
make health

# 更新服务
make update

# 备份数据
make backup

# 清理环境
make clean-docker
```

### 直接使用管理脚本

```bash
# 查看帮助
./scripts/manage.sh help

# 部署
./scripts/manage.sh deploy

# 查看特定服务日志
./scripts/manage.sh logs backend
```

## 🔍 监控和维护

### 1. 健康检查

应用提供健康检查接口：

```bash
curl http://localhost:20001/health
```

正常响应：
```json
{
  "status": "healthy",
  "version": "1.0.0",
  "services": {
    "database": "connected",
    "redis": "connected"
  }
}
```

### 2. 日志管理

```bash
# 查看实时日志
docker compose logs -f

# 查看特定服务日志
docker compose logs backend

# 查看最近的日志
docker compose logs --tail=100 backend
```

### 3. 性能监控

```bash
# 查看容器资源使用
docker stats

# 查看服务状态
docker compose ps
```

## 🔒 安全配置

### 1. 防火墙设置

```bash
# 只开放必要端口
sudo ufw allow 20001/tcp  # API端口
sudo ufw enable
```

### 2. SSL/TLS配置

建议使用反向代理（如Nginx）配置HTTPS：

```nginx
server {
    listen 443 ssl;
    server_name your-domain.com;
    
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;
    
    location / {
        proxy_pass http://localhost:20001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 🔄 更新和维护

### 1. 应用更新

```bash
# 拉取最新代码
git pull origin main

# 更新服务
make update
```

### 2. 数据备份

```bash
# 备份配置和数据
make backup

# 手动备份数据库
docker exec postgres-server pg_dump -U admin ai_novel > backup.sql
```

### 3. 故障排除

```bash
# 查看详细日志
make logs

# 重启服务
make restart

# 检查容器状态
docker compose ps

# 进入容器调试
docker compose exec backend bash
```

## 📈 扩展配置

### 1. 负载均衡

可以使用Docker Swarm或Kubernetes进行水平扩展。

### 2. 数据库优化

- 配置数据库连接池
- 设置适当的索引
- 定期清理日志

### 3. 缓存优化

- 配置Redis持久化
- 设置合适的缓存策略

## 🆘 故障排除

### 常见问题

1. **服务启动失败**
   - 检查环境变量配置
   - 确认数据库连接
   - 查看详细日志

2. **API无法访问**
   - 检查防火墙设置
   - 确认端口映射
   - 验证网络连接

3. **数据库连接失败**
   - 检查数据库服务状态
   - 验证连接字符串
   - 确认网络配置

### 获取支持

- 查看日志: `make logs`
- 检查状态: `make status`
- 健康检查: `make health`

## 📝 最佳实践

1. **定期备份**: 设置自动备份计划
2. **监控告警**: 配置服务监控和告警
3. **安全更新**: 定期更新系统和依赖
4. **性能优化**: 根据使用情况调整资源配置
5. **文档维护**: 保持部署文档的更新
