# AI小说质量优化方案

## 📋 项目概述

### 目标
通过系统化的方法提升AI生成小说的质量，解决当前存在的章节关联性差、角色一致性缺失、用户选择影响微弱等核心问题。

### 核心理念
**先解决创作逻辑，再优化技术实现** - 给AI提供完整的"作家大脑"，而不是仅仅依靠提示词工程。

## 🎯 问题分析

### 当前主要问题
1. **章节关联性缺失**：章节间关联词为0，重复开头现象严重
2. **角色一致性差**：使用"我"而非具体姓名，人物性格不鲜明
3. **世界观不稳定**：设定细节前后不一致
4. **用户选择影响微弱**：选择后故事没有明显变化

### 根本原因
- AI缺乏"记忆"机制，每次生成都像重新开始
- 缺乏角色档案和性格一致性约束
- 世界观信息传递不充分
- 缺乏选择后果的追踪和体现机制

## 🚀 解决方案架构

### 三阶段优化计划

#### 第一阶段：角色一致性系统（Week 1）
**目标**：解决人称问题，建立角色档案系统
**优先级**：🔴 最高（直接影响阅读体验）

#### 第二阶段：章节衔接机制（Week 2）
**目标**：解决重复开头和关联性问题
**优先级**：🟡 高（核心功能问题）

#### 第三阶段：选择影响系统（Week 3）
**目标**：建立真正的分支故事系统
**优先级**：🟢 中（差异化价值）

## 📊 第一阶段：角色一致性系统

### 数据库设计

#### 新增表：character_profiles
```sql
CREATE TABLE character_profiles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    story_id UUID NOT NULL REFERENCES stories(id) ON DELETE CASCADE,
    character_name VARCHAR(100) NOT NULL,
    character_role VARCHAR(50) NOT NULL, -- 主角、配角、反派
    personality_traits TEXT NOT NULL, -- 性格特点
    speaking_style TEXT, -- 说话方式
    background TEXT, -- 背景设定
    appearance TEXT, -- 外貌描述
    abilities TEXT, -- 能力设定
    relationships JSONB, -- 人际关系 {"角色名": "关系描述"}
    current_state TEXT, -- 当前状态
    growth_arc TEXT, -- 成长轨迹
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- 索引
CREATE INDEX idx_character_profiles_story_id ON character_profiles(story_id);
CREATE INDEX idx_character_profiles_role ON character_profiles(character_role);
```

#### 修改现有表：worldviews
```sql
-- 添加主角姓名字段
ALTER TABLE worldviews ADD COLUMN protagonist_name VARCHAR(100);
```

### 服务层实现

#### 1. 角色档案服务
```python
# app/services/character_service.py
class CharacterService:
    def create_protagonist_profile(self, story_id: str, worldview_data: dict) -> dict:
        """基于世界观创建主角档案"""
        
    def get_character_profile(self, story_id: str, character_role: str = "主角") -> dict:
        """获取角色档案"""
        
    def update_character_state(self, story_id: str, character_name: str, new_state: str):
        """更新角色状态"""
```

#### 2. 修改世界观创建流程
```python
# app/services/worldview_service.py
def create_worldview_with_character(self, story_id: str, worldview_data: dict):
    """创建世界观时同时创建角色档案"""
    # 1. 创建世界观
    # 2. 提取或生成主角信息
    # 3. 创建角色档案
    # 4. 关联数据
```

### 提示词优化

#### 角色档案生成提示词
```python
CHARACTER_PROFILE_PROMPT = """
基于以下世界观信息，为主角创建详细的角色档案：

世界观信息：
{worldview_info}

请按以下格式输出角色档案：

角色姓名：[具体的中文姓名，符合{style}风格]
性格特点：[3-5个核心性格特征，用逗号分隔]
说话方式：[描述角色的语言风格和表达习惯]
背景设定：[角色的出身、经历、当前处境]
外貌特征：[简洁的外貌描述]
核心能力：[角色的主要能力或天赋]
成长目标：[角色想要达成的目标]

要求：
1. 角色姓名必须具体明确，不能使用"主角"等代称
2. 性格特点要有冲突性，避免完美人设
3. 背景设定要与世界观高度契合
4. 成长目标要能支撑长篇故事发展
"""
```

#### 优化后的章节生成提示词
```python
ENHANCED_CHAPTER_PROMPT = """
{style_prompt}

{context}

**🎭 角色一致性要求（必须严格执行）：**
- 主角姓名：{character_name}
- 必须使用第三人称叙述（"他"、"{character_name}"、"这位年轻人"等）
- 严禁使用第一人称（"我"、"我的"、"我们"等）
- 角色性格特点：{personality_traits}
- 说话风格：{speaking_style}
- 角色行为必须符合既定性格，不能出现性格分裂

**🔗 强制性衔接要求（必须严格执行）：**
1. **开头衔接**：本章开头必须直接承接上章结尾场景，不得重新开始故事
2. **选择后果体现**：如果用户做出了选择，本章第一段必须立即展现选择的直接结果
3. **状态连续性**：{character_name}的位置、状态、情绪必须与上章结尾保持连续

**输出格式要求：**
首先输出章节标题：
CHAPTER_TITLE: 具体的章节标题

然后输出章节正文内容，直接从故事开始。

**内容要求：**
1. **字数要求**：章节内容必须在{min_words}-{max_words}字之间
2. **内容质量**：必须是完整的故事章节，包含详细的情节描述、人物对话、环境描写
3. **故事连贯**：严格按照上述衔接要求，确保与前章无缝连接
4. **角色塑造**：通过行动、对话、心理活动展现{character_name}的性格特点
5. **悬念设置**：章节结尾要为下一个选择做铺垫

**❌ 严格禁止的行为：**
- 使用第一人称叙述
- 重新开始故事或重复背景介绍
- 忽略用户选择的影响
- 突然改变{character_name}位置而不解释
- 与上章情节出现逻辑矛盾
- 角色行为与既定性格不符

请基于以上要求创作下一章节内容。
"""
```

### API接口设计

#### 1. 角色档案相关接口
```python
# 获取角色档案
GET /api/stories/{story_id}/characters
GET /api/stories/{story_id}/characters/{character_id}

# 更新角色状态
PUT /api/stories/{story_id}/characters/{character_id}/state
```

#### 2. 修改现有接口
```python
# 世界观创建接口增加角色档案返回
POST /api/stories/{story_id}/worldview
# 响应增加 character_profile 字段

# 章节生成接口使用角色信息
POST /api/stories/{story_id}/chapters/generate
# 内部逻辑增加角色档案获取
```

## 📈 验证指标

### 第一阶段成功标准
- [ ] 新生成的章节100%使用第三人称
- [ ] 主角姓名在章节中出现频率 > 5次
- [ ] 角色对话风格与档案设定一致
- [ ] 用户阅读体验主观评分提升

### 测试方案
1. **自动化测试**：检测章节中的人称使用
2. **A/B测试**：对比优化前后的用户反馈
3. **内容分析**：分析角色行为的一致性

## 🔄 第二阶段预览：章节衔接机制

### 核心思路
- 基于角色档案优化上下文构建
- 实现精确的状态传递机制
- 强化章节开头衔接检查

### 关键技术点
- 结构化状态管理
- 智能上下文选择
- 衔接质量自动检测

## 📝 开发任务清单

### 数据库任务
- [ ] 创建 character_profiles 表
- [ ] 修改 worldviews 表结构
- [ ] 创建相关索引
- [ ] 编写数据迁移脚本

### 后端任务
- [ ] 实现 CharacterService 类
- [ ] 修改 WorldviewService 创建流程
- [ ] 更新 AI 服务的上下文构建逻辑
- [ ] 修改章节生成提示词
- [ ] 添加角色档案相关 API 接口

### 前端任务（可选）
- [ ] 世界观创建页面显示生成的角色信息
- [ ] 故事详情页面显示角色档案
- [ ] 角色状态可视化组件

### 测试任务
- [ ] 单元测试：角色档案 CRUD 操作
- [ ] 集成测试：世界观创建流程
- [ ] 端到端测试：完整的故事生成流程
- [ ] 内容质量测试：人称使用检测

## 🎯 预期收益

### 短期收益（第一阶段完成后）
- 解决最明显的阅读体验问题
- 建立角色一致性基础架构
- 为后续优化奠定数据基础

### 长期收益（三阶段完成后）
- 显著提升故事连贯性和可读性
- 建立真正的交互式小说体验
- 形成可复制的AI内容生成方法论

## 💻 详细实现代码

### 1. 角色档案服务实现

```python
# app/services/character_service.py
from typing import Dict, List, Optional
from app.models.character import CharacterProfile
from app.database import get_db_connection

class CharacterService:

    def create_protagonist_profile(self, story_id: str, worldview_data: dict) -> dict:
        """基于世界观创建主角档案"""

        # 构建角色档案生成提示词
        prompt = self._build_character_creation_prompt(worldview_data)

        # 调用AI生成角色档案
        ai_response = self.ai_service.generate_character_profile(prompt)

        # 解析AI响应
        character_data = self._parse_character_response(ai_response)

        # 保存到数据库
        character_profile = CharacterProfile(
            story_id=story_id,
            character_name=character_data['name'],
            character_role="主角",
            personality_traits=character_data['personality'],
            speaking_style=character_data['speaking_style'],
            background=character_data['background'],
            appearance=character_data['appearance'],
            abilities=character_data['abilities'],
            growth_arc=character_data['growth_goal']
        )

        return self._save_character_profile(character_profile)

    def get_character_context(self, story_id: str) -> dict:
        """获取用于章节生成的角色上下文"""
        protagonist = self.get_character_profile(story_id, "主角")

        return {
            "character_name": protagonist.character_name,
            "personality_traits": protagonist.personality_traits,
            "speaking_style": protagonist.speaking_style,
            "current_state": protagonist.current_state or "正常状态"
        }

    def _build_character_creation_prompt(self, worldview_data: dict) -> str:
        """构建角色创建提示词"""
        return f"""
基于以下世界观信息，为主角创建详细的角色档案：

世界观信息：
世界设定：{worldview_data.get('world_setting', '')}
力量体系：{worldview_data.get('power_system', '')}
主角描述：{worldview_data.get('main_character', '')}
故事风格：{worldview_data.get('style', '')}

请按以下格式输出角色档案：

角色姓名：[具体的中文姓名，符合仙侠风格]
性格特点：[3-5个核心性格特征，用逗号分隔]
说话方式：[描述角色的语言风格和表达习惯]
背景设定：[角色的出身、经历、当前处境]
外貌特征：[简洁的外貌描述]
核心能力：[角色的主要能力或天赋]
成长目标：[角色想要达成的目标]

要求：
1. 角色姓名必须具体明确，不能使用"主角"等代称
2. 性格特点要有冲突性，避免完美人设
3. 背景设定要与世界观高度契合
4. 成长目标要能支撑长篇故事发展
"""
```

### 2. 修改后的AI服务

```python
# app/services/ai_service.py
class AIService:

    def generate_chapter_stream(self, story_id: str, user_choice: str = None):
        """生成章节（集成角色档案）"""

        # 获取角色上下文
        character_context = self.character_service.get_character_context(story_id)

        # 构建增强的上下文
        context = self.prompt_manager.build_enhanced_context(
            story_id=story_id,
            character_context=character_context,
            user_choice=user_choice
        )

        # 使用优化后的提示词生成章节
        prompt = self.prompt_manager.get_enhanced_chapter_prompt(
            context=context,
            character_context=character_context
        )

        # 流式生成
        for chunk in self.llm_provider.generate_stream(prompt):
            yield chunk
```

### 3. 数据库模型

```python
# app/models/character.py
from sqlalchemy import Column, String, Text, DateTime, ForeignKey
from sqlalchemy.dialects.postgresql import UUID, JSONB
from app.database import Base
import uuid

class CharacterProfile(Base):
    __tablename__ = "character_profiles"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    story_id = Column(UUID(as_uuid=True), ForeignKey("stories.id"), nullable=False)
    character_name = Column(String(100), nullable=False)
    character_role = Column(String(50), nullable=False)
    personality_traits = Column(Text, nullable=False)
    speaking_style = Column(Text)
    background = Column(Text)
    appearance = Column(Text)
    abilities = Column(Text)
    relationships = Column(JSONB)
    current_state = Column(Text)
    growth_arc = Column(Text)
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())
```

### 4. API路由实现

```python
# app/api/characters.py
from fastapi import APIRouter, Depends, HTTPException
from app.services.character_service import CharacterService

router = APIRouter()

@router.get("/stories/{story_id}/characters")
async def get_story_characters(
    story_id: str,
    character_service: CharacterService = Depends()
):
    """获取故事的所有角色"""
    characters = character_service.get_story_characters(story_id)
    return {"characters": characters}

@router.get("/stories/{story_id}/characters/protagonist")
async def get_protagonist(
    story_id: str,
    character_service: CharacterService = Depends()
):
    """获取主角信息"""
    protagonist = character_service.get_character_profile(story_id, "主角")
    if not protagonist:
        raise HTTPException(status_code=404, detail="主角信息未找到")
    return protagonist

@router.put("/stories/{story_id}/characters/{character_id}/state")
async def update_character_state(
    story_id: str,
    character_id: str,
    state_data: dict,
    character_service: CharacterService = Depends()
):
    """更新角色状态"""
    updated_character = character_service.update_character_state(
        character_id, state_data["current_state"]
    )
    return updated_character
```

## 🧪 测试用例

### 1. 角色档案生成测试

```python
# tests/test_character_service.py
import pytest
from app.services.character_service import CharacterService

class TestCharacterService:

    def test_create_protagonist_profile(self):
        """测试主角档案创建"""
        worldview_data = {
            "world_setting": "青云大陆，灵气浓郁的修仙世界",
            "power_system": "炼气、筑基、金丹、元婴等修炼境界",
            "main_character": "天赋异禀的年轻修士",
            "style": "XIANXIA"
        }

        character_service = CharacterService()
        profile = character_service.create_protagonist_profile(
            story_id="test-story-id",
            worldview_data=worldview_data
        )

        assert profile["character_name"] is not None
        assert "我" not in profile["character_name"]  # 确保不使用第一人称
        assert len(profile["personality_traits"]) > 0
        assert profile["character_role"] == "主角"

    def test_character_context_generation(self):
        """测试角色上下文生成"""
        character_service = CharacterService()
        context = character_service.get_character_context("test-story-id")

        assert "character_name" in context
        assert "personality_traits" in context
        assert "speaking_style" in context
```

### 2. 章节生成人称检测

```python
# tests/test_chapter_generation.py
import re
from app.services.ai_service import AIService

class TestChapterGeneration:

    def test_third_person_narration(self):
        """测试第三人称叙述"""
        ai_service = AIService()

        # 生成测试章节
        chapter_content = ai_service.generate_chapter_for_test(
            story_id="test-story-id",
            user_choice="测试选择"
        )

        # 检测第一人称使用
        first_person_patterns = [r'\b我\b', r'\b我的\b', r'\b我们\b']

        for pattern in first_person_patterns:
            matches = re.findall(pattern, chapter_content)
            assert len(matches) == 0, f"发现第一人称使用: {matches}"

        # 检测主角姓名出现
        character_name = self._get_character_name("test-story-id")
        name_count = chapter_content.count(character_name)
        assert name_count >= 3, f"主角姓名出现次数不足: {name_count}"
```

## 📞 技术支持

如有疑问，请联系产品负责人进行澄清和讨论。

### 开发流程建议
1. **先实现数据库层**：创建表结构和模型
2. **再实现服务层**：角色档案的CRUD操作
3. **然后集成AI服务**：修改章节生成逻辑
4. **最后添加API接口**：提供前端调用能力
5. **编写测试用例**：确保功能正确性

### 关键注意事项
- 角色姓名生成要符合故事风格（仙侠、科幻等）
- 提示词修改要保持向后兼容
- 数据库迁移要考虑现有数据
- 测试要覆盖人称使用检测

---
*文档版本：v1.0*
*创建时间：2025-08-01*
*最后更新：2025-08-01*
