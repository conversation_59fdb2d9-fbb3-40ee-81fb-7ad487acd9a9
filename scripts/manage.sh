#!/bin/bash

# InkFlow AI 生产环境管理脚本

set -e

COMPOSE_FILE="docker-compose.yml"

show_help() {
    echo "InkFlow AI 生产环境管理工具"
    echo ""
    echo "用法: $0 [命令]"
    echo ""
    echo "命令:"
    echo "  deploy    部署生产环境"
    echo "  start     启动服务"
    echo "  stop      停止服务"
    echo "  restart   重启服务"
    echo "  status    查看服务状态"
    echo "  logs      查看日志"
    echo "  health    检查健康状态"
    echo "  update    更新服务（重新构建并部署）"
    echo "  clean     清理环境（删除容器和镜像）"
    echo "  backup    备份数据"
    echo "  help      显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 deploy         # 部署生产环境"
    echo "  $0 logs           # 查看所有日志"
    echo "  $0 logs backend   # 查看后端日志"
    echo "  $0 health         # 检查健康状态"
    echo ""
}

check_docker() {
    if ! docker info > /dev/null 2>&1; then
        echo "❌ Docker 未运行，请先启动 Docker"
        exit 1
    fi
}

check_files() {
    if [ ! -f ".env" ]; then
        echo "❌ .env 文件不存在，请先创建生产环境配置文件"
        exit 1
    fi
    
    if [ ! -f "$COMPOSE_FILE" ]; then
        echo "❌ $COMPOSE_FILE 文件不存在"
        exit 1
    fi
}

deploy() {
    echo "🚀 部署生产环境..."
    check_docker
    check_files
    
    # 停止现有服务
    docker compose -f $COMPOSE_FILE down --remove-orphans || true
    
    # 构建镜像
    echo "🔨 构建生产镜像..."
    docker build -t inkflow-ai-backend:latest .
    
    # 启动服务
    echo "🚀 启动服务..."
    docker compose -f $COMPOSE_FILE up -d
    
    # 等待并检查健康状态
    sleep 10
    health_check
    
    echo "✅ 部署完成！"
}

start() {
    echo "🚀 启动服务..."
    check_docker
    check_files
    docker compose -f $COMPOSE_FILE up -d
    echo "✅ 服务已启动"
}

stop() {
    echo "🛑 停止服务..."
    docker compose -f $COMPOSE_FILE down
    echo "✅ 服务已停止"
}

restart() {
    echo "🔄 重启服务..."
    docker compose -f $COMPOSE_FILE restart
    echo "✅ 服务已重启"
}

status() {
    echo "📊 服务状态："
    docker compose -f $COMPOSE_FILE ps
}

logs() {
    if [ -n "$2" ]; then
        echo "📋 查看 $2 服务日志："
        docker compose -f $COMPOSE_FILE logs -f "$2"
    else
        echo "📋 查看所有服务日志："
        docker compose -f $COMPOSE_FILE logs -f
    fi
}

health_check() {
    echo "🔍 检查健康状态..."
    
    max_attempts=30
    attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s http://localhost:20001/health > /dev/null 2>&1; then
            echo "✅ 后端API 健康状态正常"
            
            # 获取详细健康信息
            health_info=$(curl -s http://localhost:20001/health)
            echo "📊 健康信息: $health_info"
            return 0
        else
            echo "⏳ 等待API启动... ($attempt/$max_attempts)"
            sleep 2
            ((attempt++))
        fi
    done
    
    echo "❌ 健康检查失败"
    return 1
}

update() {
    echo "🔄 更新服务..."
    check_docker
    check_files
    
    # 停止服务
    docker compose -f $COMPOSE_FILE down
    
    # 重新构建镜像
    echo "🔨 重新构建镜像..."
    docker build --no-cache -t inkflow-ai-backend:latest .
    
    # 启动服务
    echo "🚀 启动更新后的服务..."
    docker compose -f $COMPOSE_FILE up -d
    
    # 检查健康状态
    sleep 10
    health_check
    
    echo "✅ 更新完成！"
}

clean() {
    echo "🧹 清理环境..."
    
    # 停止并删除容器
    docker compose -f $COMPOSE_FILE down --remove-orphans
    
    # 删除镜像
    docker rmi inkflow-ai-backend:latest || true
    
    # 清理未使用的镜像
    docker image prune -f
    
    echo "✅ 清理完成"
}

backup() {
    echo "💾 备份数据..."
    
    timestamp=$(date +"%Y%m%d_%H%M%S")
    backup_dir="backups/$timestamp"
    
    mkdir -p "$backup_dir"
    
    # 备份环境配置
    cp .env "$backup_dir/"
    
    # 备份Docker配置
    cp docker-compose.yml "$backup_dir/"
    cp Dockerfile "$backup_dir/"
    
    echo "✅ 备份完成: $backup_dir"
}

# 主逻辑
case "$1" in
    deploy)
        deploy
        ;;
    start)
        start
        ;;
    stop)
        stop
        ;;
    restart)
        restart
        ;;
    status)
        status
        ;;
    logs)
        logs "$@"
        ;;
    health)
        health_check
        ;;
    update)
        update
        ;;
    clean)
        clean
        ;;
    backup)
        backup
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        echo "❌ 未知命令: $1"
        echo ""
        show_help
        exit 1
        ;;
esac
