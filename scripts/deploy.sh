#!/bin/bash

# InkFlow AI 生产环境部署脚本

set -e

echo "🚀 InkFlow AI 生产环境部署"
echo "================================"

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker 未运行，请先启动 Docker"
    exit 1
fi

# 检查必要文件
if [ ! -f ".env" ]; then
    echo "❌ .env 文件不存在，请先创建生产环境配置文件"
    exit 1
fi

if [ ! -f "Dockerfile" ]; then
    echo "❌ Dockerfile 文件不存在"
    exit 1
fi

if [ ! -f "docker-compose.yml" ]; then
    echo "❌ docker-compose.yml 文件不存在"
    exit 1
fi

echo "📋 检查环境配置..."
echo "✅ Docker 运行正常"
echo "✅ 生产环境配置文件存在"

# 停止现有服务
echo "🛑 停止现有服务..."
docker compose down --remove-orphans || true

# 构建生产镜像
echo "🔨 构建生产环境镜像..."
docker build -t inkflow-ai-backend:latest .

# 启动服务
echo "🚀 启动生产环境服务..."
docker compose up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 15

# 检查服务状态
echo "📊 检查服务状态..."
docker compose ps

# 检查后端API健康状态
echo "🔍 检查后端API健康状态..."
max_attempts=30
attempt=1

while [ $attempt -le $max_attempts ]; do
    if curl -s http://localhost:20001/health > /dev/null 2>&1; then
        echo "✅ 后端API 运行正常"
        echo "🌐 API地址: http://localhost:20001"
        echo "📚 API文档: http://localhost:20001/docs"
        break
    else
        echo "⏳ 等待API启动... ($attempt/$max_attempts)"
        sleep 2
        ((attempt++))
    fi
done

if [ $attempt -gt $max_attempts ]; then
    echo "❌ 后端API 启动失败，请检查日志"
    echo "📋 查看日志: docker compose logs backend"
    exit 1
fi

echo ""
echo "🎉 生产环境部署完成！"
echo "================================"
echo "📝 管理命令："
echo "  查看日志: docker compose logs -f"
echo "  停止服务: docker compose down"
echo "  重启服务: docker compose restart"
echo "  查看状态: docker compose ps"
echo ""
echo "🔧 维护说明："
echo "  - 服务运行在生产模式"
echo "  - 使用 .env 环境配置"
echo "  - 数据库连接到外部 database-network"
echo ""
