# InkFlow AI - 前端开发API文档

## 🎯 快速开始

### 基础信息
- **API基础URL**: `http://localhost:20001/api`
- **认证方式**: JWT Bearer Token
- **内容类型**: `application/json`
- **字符编码**: UTF-8

### 前端集成示例

```javascript
// API基础配置
const API_BASE_URL = 'http://localhost:20001/api';

// 创建axios实例
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器 - 自动添加token
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('access_token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});
```

## 🔐 用户认证

### 1. 用户注册

**接口**: `POST /auth/register`

**请求示例**:
```javascript
const register = async (username, password) => {
  try {
    const response = await api.post('/auth/register', {
      username,
      password
    });
    return response.data;
  } catch (error) {
    throw error.response.data;
  }
};
```

**请求体**:
```json
{
  "username": "testuser",
  "password": "password123"
}
```

**成功响应** (201):
```json
{
  "success": true,
  "message": "用户注册成功",
  "data": {
    "user_id": "uuid-string",
    "username": "testuser"
  }
}
```

**错误响应** (400):
```json
{
  "success": false,
  "error": "用户名已存在",
  "detail": "Username already exists"
}
```

### 2. 用户登录

**接口**: `POST /auth/login`

**请求示例**:
```javascript
const login = async (username, password) => {
  try {
    const response = await api.post('/auth/login', {
      username,
      password
    });

    // 保存token到localStorage
    localStorage.setItem('access_token', response.data.access_token);

    return response.data;
  } catch (error) {
    throw error.response.data;
  }
};
```

**请求体**:
```json
{
  "username": "testuser",
  "password": "password123"
}
```

**成功响应** (200):
```json
{
  "success": true,
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "user": {
    "user_id": "uuid-string",
    "username": "testuser"
  }
}
```

### 3. 获取用户信息

**接口**: `GET /auth/me`

**请求示例**:
```javascript
const getCurrentUser = async () => {
  try {
    const response = await api.get('/auth/me');
    return response.data;
  } catch (error) {
    // Token过期或无效，跳转到登录页
    if (error.response.status === 401) {
      localStorage.removeItem('access_token');
      window.location.href = '/login';
    }
    throw error.response.data;
  }
};
```

**成功响应** (200):
```json
{
  "success": true,
  "data": {
    "user_id": "uuid-string",
    "username": "testuser",
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

## 📚 故事管理

### 1. 获取故事列表

**接口**: `GET /stories`

**请求示例**:
```javascript
const getStories = async () => {
  try {
    const response = await api.get('/stories');
    return response.data;
  } catch (error) {
    throw error.response.data;
  }
};
```

**成功响应** (200):
```json
{
  "success": true,
  "data": [
    {
      "id": "story-uuid-1",
      "title": "修仙之路",
      "style": "xianxia",
      "current_chapter_number": 3,
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-02T00:00:00Z"
    },
    {
      "id": "story-uuid-2",
      "title": "都市传说",
      "style": "urban",
      "current_chapter_number": 1,
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z"
    }
  ],
  "message": "获取故事列表成功"
}
```

### 2. 创建新故事

**接口**: `POST /stories`

**请求示例**:
```javascript
const createStory = async (storyData) => {
  try {
    const response = await api.post('/stories', {
      style: storyData.style,
      title: storyData.title, // 可选
      theme: storyData.theme, // 可选
      provider: 'siliconflow' // 可选，默认siliconflow
    });
    return response.data;
  } catch (error) {
    throw error.response.data;
  }
};

// 使用示例
const newStory = await createStory({
  style: 'xianxia',
  title: '我的修仙之路',
  theme: '少年修仙成长记'
});
```

**请求体**:
```json
{
  "style": "xianxia",
  "title": "我的修仙之路",
  "theme": "少年修仙成长记",
  "provider": "siliconflow"
}
```

**故事风格选项**:
- `xianxia`: 修仙
- `urban`: 都市
- `scifi`: 科幻
- `fantasy`: 奇幻
- `mystery`: 悬疑
- `romance`: 言情

**成功响应** (201):
```json
{
  "success": true,
  "data": {
    "id": "story-uuid",
    "title": "我的修仙之路",
    "style": "xianxia",
    "theme": "少年修仙成长记",
    "current_chapter_number": 0,
    "worldview": {
      "setting": "修仙世界，灵气复苏",
      "power_system": "筑基、金丹、元婴、化神",
      "main_character": "天赋异禀的少年",
      "background": "现代都市中隐藏的修仙世界"
    },
    "created_at": "2024-01-01T00:00:00Z"
  },
  "message": "故事创建成功"
}
```

### 3. 获取故事详情

**接口**: `GET /stories/{story_id}`

**请求示例**:
```javascript
const getStoryDetail = async (storyId) => {
  try {
    const response = await api.get(`/stories/${storyId}`);
    return response.data;
  } catch (error) {
    throw error.response.data;
  }
};
```

**成功响应** (200):
```json
{
  "success": true,
  "data": {
    "id": "story-uuid",
    "title": "我的修仙之路",
    "style": "xianxia",
    "theme": "少年修仙成长记",
    "current_chapter_number": 3,
    "worldview": {
      "setting": "修仙世界，灵气复苏",
      "power_system": "筑基、金丹、元婴、化神",
      "main_character": "天赋异禀的少年",
      "background": "现代都市中隐藏的修仙世界"
    },
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-02T00:00:00Z"
  }
}
```

### 4. 获取故事章节列表

**接口**: `GET /stories/{story_id}/chapters`

**请求示例**:
```javascript
const getChapters = async (storyId) => {
  try {
    const response = await api.get(`/stories/${storyId}/chapters`);
    return response.data;
  } catch (error) {
    throw error.response.data;
  }
};
```

**成功响应** (200):
```json
{
  "success": true,
  "data": [
    {
      "id": "chapter-uuid-1",
      "chapter_number": 1,
      "title": "觉醒",
      "content": "在这个平凡的都市中...",
      "created_at": "2024-01-01T00:00:00Z"
    },
    {
      "id": "chapter-uuid-2",
      "chapter_number": 2,
      "title": "初入修仙界",
      "content": "随着神秘老者的指引...",
      "created_at": "2024-01-01T12:00:00Z"
    }
  ]
}
```

### 5. 流式生成章节 ⭐ (核心接口)

**接口**: `POST /stories/{story_id}/chapters/generate/stream`

这是最重要的接口，用于生成第一章和后续章节。

**请求示例**:
```javascript
// 生成第一章
const generateFirstChapter = async (storyId) => {
  try {
    const response = await fetch(`${API_BASE_URL}/stories/${storyId}/chapters/generate/stream`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        provider: 'siliconflow' // 可选
      })
    });

    const reader = response.body.getReader();
    const decoder = new TextDecoder();

    while (true) {
      const { done, value } = await reader.read();
      if (done) break;

      const chunk = decoder.decode(value);
      const lines = chunk.split('\n');

      for (const line of lines) {
        if (line.startsWith('data: ')) {
          const data = JSON.parse(line.slice(6));
          handleStreamData(data);
        }
      }
    }
  } catch (error) {
    console.error('生成章节失败:', error);
  }
};

// 生成后续章节
const generateNextChapter = async (storyId, userChoice) => {
  const response = await fetch(`${API_BASE_URL}/stories/${storyId}/chapters/generate/stream`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      provider: 'siliconflow',
      user_choice: userChoice,
      choice_type: 'ai_generated'
    })
  });
  // ... 处理流式响应
};

// 处理流式数据
const handleStreamData = (data) => {
  switch (data.type) {
    case 'start':
      console.log('开始生成:', data.message);
      break;
    case 'title':
      console.log('章节标题:', data.content);
      break;
    case 'content':
      // 实时显示生成的内容
      appendContent(data.content);
      break;
    case 'complete':
      console.log('生成完成');
      const { title, content, choices } = data.data;
      showChapterComplete(title, content, choices);
      break;
    case 'error':
      console.error('生成错误:', data.message);
      break;
  }
};
```

**请求体 (第一章)**:
```json
{
  "provider": "siliconflow"
}
```

**请求体 (后续章节)**:
```json
{
  "provider": "siliconflow",
  "user_choice": "选择探索神秘的洞穴",
  "choice_type": "ai_generated"
}
```

**流式响应格式** (Server-Sent Events):
```
data: {"type": "start", "message": "开始生成第一章..."}

data: {"type": "title", "content": "觉醒"}

data: {"type": "content", "content": "在这个平凡的都市中，"}

data: {"type": "content", "content": "少年林风过着普通的生活..."}

data: {"type": "complete", "data": {"title": "觉醒", "content": "完整章节内容...", "choices": ["选择1", "选择2", "选择3"]}}
```

### 6. 获取选择历史

**接口**: `GET /stories/{story_id}/choices`

**请求示例**:
```javascript
const getChoicesHistory = async (storyId) => {
  try {
    const response = await api.get(`/stories/${storyId}/choices`);
    return response.data;
  } catch (error) {
    throw error.response.data;
  }
};
```

**成功响应** (200):
```json
{
  "success": true,
  "data": [
    {
      "chapter_number": 1,
      "choice_text": "选择探索神秘的洞穴",
      "choice_type": "ai_generated",
      "created_at": "2024-01-01T12:00:00Z"
    },
    {
      "chapter_number": 2,
      "choice_text": "决定拜师学艺",
      "choice_type": "ai_generated",
      "created_at": "2024-01-01T18:00:00Z"
    }
  ]
}
```

### 7. 删除故事

**接口**: `DELETE /stories/{story_id}`

**请求示例**:
```javascript
const deleteStory = async (storyId) => {
  try {
    const response = await api.delete(`/stories/${storyId}`);
    return response.data;
  } catch (error) {
    throw error.response.data;
  }
};
```

**成功响应** (200):
```json
{
  "success": true,
  "message": "故事删除成功"
}
```

## 📖 章节管理

### 1. 获取章节内容

**接口**: `GET /chapters/{chapter_id}`

**请求示例**:
```javascript
const getChapter = async (chapterId) => {
  try {
    const response = await api.get(`/chapters/${chapterId}`);
    return response.data;
  } catch (error) {
    throw error.response.data;
  }
};
```

**成功响应** (200):
```json
{
  "success": true,
  "data": {
    "id": "chapter-uuid",
    "chapter_number": 1,
    "title": "觉醒",
    "content": "在这个平凡的都市中，少年林风过着普通的生活...",
    "story_id": "story-uuid",
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

### 2. 获取章节详情 (包含选择选项)

**接口**: `GET /chapters/{chapter_id}/detail`

**请求示例**:
```javascript
const getChapterDetail = async (chapterId) => {
  try {
    const response = await api.get(`/chapters/${chapterId}/detail`);
    return response.data;
  } catch (error) {
    throw error.response.data;
  }
};
```

**成功响应** (200):
```json
{
  "success": true,
  "data": {
    "chapter": {
      "id": "chapter-uuid",
      "chapter_number": 1,
      "title": "觉醒",
      "content": "在这个平凡的都市中，少年林风过着普通的生活...",
      "story_id": "story-uuid",
      "created_at": "2024-01-01T00:00:00Z"
    },
    "choices": [
      {
        "id": "choice-uuid-1",
        "text": "选择探索神秘的洞穴",
        "order": 1
      },
      {
        "id": "choice-uuid-2",
        "text": "决定回家继续学习",
        "order": 2
      },
      {
        "id": "choice-uuid-3",
        "text": "寻找其他线索",
        "order": 3
      }
    ]
  }
}
```

### 3. 提交选择并生成下一章

**接口**: `POST /chapters/{chapter_id}/choices`

**请求示例**:
```javascript
const submitChoice = async (chapterId, choiceId, customChoice = null) => {
  try {
    const response = await api.post(`/chapters/${chapterId}/choices`, {
      choice_id: choiceId,
      custom_choice: customChoice,
      provider: 'siliconflow'
    });
    return response.data;
  } catch (error) {
    throw error.response.data;
  }
};

// 使用AI生成的选择
await submitChoice('chapter-uuid', 'choice-uuid-1');

// 使用自定义选择
await submitChoice('chapter-uuid', null, '我要寻找传说中的仙人');
```

**请求体**:
```json
{
  "choice_id": "choice-uuid-1",
  "custom_choice": null,
  "provider": "siliconflow"
}
```

**成功响应** (200):
```json
{
  "success": true,
  "message": "选择已提交，开始生成下一章",
  "data": {
    "next_chapter_number": 2,
    "choice_saved": true
  }
}
```

## 🏥 系统接口

### 健康检查

**接口**: `GET /health`

**请求示例**:
```javascript
const checkHealth = async () => {
  try {
    const response = await fetch(`${API_BASE_URL.replace('/api', '')}/health`);
    return await response.json();
  } catch (error) {
    throw error;
  }
};
```

**成功响应** (200):
```json
{
  "status": "healthy",
  "version": "1.0.0",
  "services": {
    "database": "connected",
    "redis": "connected"
  }
}
```

## 🎨 前端集成指南

### 完整的React Hook示例

```javascript
// hooks/useInkFlowAPI.js
import { useState, useEffect } from 'react';
import axios from 'axios';

const API_BASE_URL = 'http://localhost:20001/api';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('access_token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// 响应拦截器
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('access_token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

export const useAuth = () => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  const login = async (username, password) => {
    try {
      const response = await api.post('/auth/login', { username, password });
      localStorage.setItem('access_token', response.data.access_token);
      setUser(response.data.user);
      return response.data;
    } catch (error) {
      throw error.response.data;
    }
  };

  const register = async (username, password) => {
    try {
      const response = await api.post('/auth/register', { username, password });
      return response.data;
    } catch (error) {
      throw error.response.data;
    }
  };

  const logout = () => {
    localStorage.removeItem('access_token');
    setUser(null);
  };

  useEffect(() => {
    const checkAuth = async () => {
      try {
        const response = await api.get('/auth/me');
        setUser(response.data.data);
      } catch (error) {
        localStorage.removeItem('access_token');
      } finally {
        setLoading(false);
      }
    };

    if (localStorage.getItem('access_token')) {
      checkAuth();
    } else {
      setLoading(false);
    }
  }, []);

  return { user, login, register, logout, loading };
};

export const useStories = () => {
  const [stories, setStories] = useState([]);
  const [loading, setLoading] = useState(false);

  const fetchStories = async () => {
    setLoading(true);
    try {
      const response = await api.get('/stories');
      setStories(response.data.data);
    } catch (error) {
      console.error('获取故事列表失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const createStory = async (storyData) => {
    try {
      const response = await api.post('/stories', storyData);
      await fetchStories(); // 刷新列表
      return response.data;
    } catch (error) {
      throw error.response.data;
    }
  };

  const deleteStory = async (storyId) => {
    try {
      await api.delete(`/stories/${storyId}`);
      await fetchStories(); // 刷新列表
    } catch (error) {
      throw error.response.data;
    }
  };

  return { stories, loading, fetchStories, createStory, deleteStory };
};

export const useChapterGeneration = () => {
  const [isGenerating, setIsGenerating] = useState(false);
  const [currentContent, setCurrentContent] = useState('');
  const [currentTitle, setCurrentTitle] = useState('');

  const generateChapter = async (storyId, userChoice = null) => {
    setIsGenerating(true);
    setCurrentContent('');
    setCurrentTitle('');

    try {
      const response = await fetch(`${API_BASE_URL}/stories/${storyId}/chapters/generate/stream`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          provider: 'siliconflow',
          user_choice: userChoice,
          choice_type: userChoice ? 'ai_generated' : undefined
        })
      });

      const reader = response.body.getReader();
      const decoder = new TextDecoder();

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value);
        const lines = chunk.split('\n');

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = JSON.parse(line.slice(6));

            switch (data.type) {
              case 'title':
                setCurrentTitle(data.content);
                break;
              case 'content':
                setCurrentContent(prev => prev + data.content);
                break;
              case 'complete':
                setIsGenerating(false);
                return data.data; // 返回完整数据
              case 'error':
                throw new Error(data.message);
            }
          }
        }
      }
    } catch (error) {
      setIsGenerating(false);
      throw error;
    }
  };

  return {
    isGenerating,
    currentContent,
    currentTitle,
    generateChapter
  };
};
```

### 错误处理最佳实践

```javascript
// utils/errorHandler.js
export const handleAPIError = (error) => {
  if (error.response) {
    // 服务器返回错误状态码
    const { status, data } = error.response;

    switch (status) {
      case 400:
        return data.error || '请求参数错误';
      case 401:
        return '登录已过期，请重新登录';
      case 403:
        return '没有权限访问此资源';
      case 404:
        return '请求的资源不存在';
      case 500:
        return '服务器内部错误，请稍后重试';
      default:
        return data.error || '未知错误';
    }
  } else if (error.request) {
    // 网络错误
    return '网络连接失败，请检查网络设置';
  } else {
    // 其他错误
    return error.message || '发生未知错误';
  }
};
```

## 📊 API状态码说明

| 状态码 | 说明 | 处理建议 |
|--------|------|----------|
| 200 | 请求成功 | 正常处理响应数据 |
| 201 | 创建成功 | 资源创建成功，可获取新资源信息 |
| 400 | 请求错误 | 检查请求参数，显示错误信息 |
| 401 | 未授权 | 清除token，跳转到登录页 |
| 403 | 禁止访问 | 显示权限不足提示 |
| 404 | 资源不存在 | 显示资源不存在提示 |
| 500 | 服务器错误 | 显示服务器错误，建议重试 |

## 🚀 快速开始示例

```javascript
// 完整的故事创建和章节生成流程
const createAndGenerateStory = async () => {
  try {
    // 1. 创建故事
    const story = await createStory({
      style: 'xianxia',
      title: '我的修仙之路',
      theme: '少年修仙成长记'
    });

    // 2. 生成第一章
    const firstChapter = await generateChapter(story.data.id);

    // 3. 用户选择
    const userChoice = firstChapter.choices[0];

    // 4. 生成第二章
    const secondChapter = await generateChapter(story.data.id, userChoice);

    console.log('故事创建和章节生成完成！');
  } catch (error) {
    console.error('操作失败:', handleAPIError(error));
  }
};
```

这份文档应该能帮助您快速开始前端开发！如果需要更多特定功能的示例，请告诉我。
