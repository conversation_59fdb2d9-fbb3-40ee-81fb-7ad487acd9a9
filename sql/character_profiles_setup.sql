-- =====================================================
-- AI小说质量优化 - 角色档案系统数据库脚本
-- =====================================================
-- 用途：为AI小说生成系统添加角色一致性支持
-- 数据库：PostgreSQL
-- 执行方式：psql -U admin -d ai_novel -f character_profiles_setup.sql
-- =====================================================

-- 确保UUID扩展已启用
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 检查依赖表是否存在
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'stories') THEN
        RAISE EXCEPTION 'stories表不存在，请先确保基础表结构已创建';
    END IF;
END $$;

-- =====================================================
-- 1. 创建角色档案表
-- =====================================================

CREATE TABLE IF NOT EXISTS character_profiles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    story_id UUID NOT NULL REFERENCES stories(id) ON DELETE CASCADE,
    character_name VARCHAR(100) NOT NULL,
    character_role VARCHAR(50) NOT NULL DEFAULT '主角',
    personality_traits TEXT NOT NULL,
    speaking_style TEXT,
    background TEXT,
    appearance TEXT,
    abilities TEXT,
    relationships JSONB DEFAULT '{}',
    current_state TEXT,
    growth_arc TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- 约束
    CONSTRAINT chk_character_name_not_empty CHECK (LENGTH(TRIM(character_name)) > 0),
    CONSTRAINT chk_personality_not_empty CHECK (LENGTH(TRIM(personality_traits)) > 0),
    CONSTRAINT chk_valid_role CHECK (character_role IN ('主角', '配角', '反派', '其他'))
);

-- =====================================================
-- 2. 创建索引
-- =====================================================

-- 主要查询索引
CREATE INDEX IF NOT EXISTS idx_character_profiles_story_id 
ON character_profiles(story_id);

-- 角色类型索引
CREATE INDEX IF NOT EXISTS idx_character_profiles_role 
ON character_profiles(character_role);

-- 角色姓名索引（用于快速查找）
CREATE INDEX IF NOT EXISTS idx_character_profiles_name 
ON character_profiles(character_name);

-- 复合索引（故事+角色类型）
CREATE INDEX IF NOT EXISTS idx_character_profiles_story_role 
ON character_profiles(story_id, character_role);

-- =====================================================
-- 3. 修改现有表结构
-- =====================================================

-- 为worldviews表添加主角姓名字段
ALTER TABLE worldviews 
ADD COLUMN IF NOT EXISTS protagonist_name VARCHAR(100);

-- 添加注释
COMMENT ON COLUMN worldviews.protagonist_name IS '主角姓名，用于角色一致性检查';

-- =====================================================
-- 4. 创建触发器（自动更新时间戳）
-- =====================================================

-- 创建更新时间戳函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为character_profiles表创建触发器
DROP TRIGGER IF EXISTS update_character_profiles_updated_at ON character_profiles;
CREATE TRIGGER update_character_profiles_updated_at
    BEFORE UPDATE ON character_profiles
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- 5. 创建视图（便于查询）
-- =====================================================

-- 主角信息视图
CREATE OR REPLACE VIEW protagonist_info AS
SELECT 
    s.id as story_id,
    s.title as story_title,
    s.style as story_style,
    cp.character_name,
    cp.personality_traits,
    cp.speaking_style,
    cp.background,
    cp.appearance,
    cp.abilities,
    cp.current_state,
    cp.growth_arc,
    cp.created_at,
    cp.updated_at
FROM stories s
LEFT JOIN character_profiles cp ON s.id = cp.story_id AND cp.character_role = '主角';

-- 角色统计视图
CREATE OR REPLACE VIEW character_stats AS
SELECT 
    story_id,
    COUNT(*) as total_characters,
    COUNT(CASE WHEN character_role = '主角' THEN 1 END) as protagonists,
    COUNT(CASE WHEN character_role = '配角' THEN 1 END) as supporting_characters,
    COUNT(CASE WHEN character_role = '反派' THEN 1 END) as antagonists
FROM character_profiles
GROUP BY story_id;

-- =====================================================
-- 6. 插入示例数据（可选）
-- =====================================================

-- 为测试创建示例角色档案
-- 注意：这里使用的story_id需要在实际的stories表中存在
/*
INSERT INTO character_profiles (
    story_id, 
    character_name, 
    character_role, 
    personality_traits, 
    speaking_style, 
    background,
    appearance,
    abilities,
    growth_arc
) VALUES (
    '00000000-0000-0000-0000-000000000001', -- 替换为实际的story_id
    '林浩',
    '主角',
    '倔强不屈,正义感强,容易冲动,聪明机敏,重情重义',
    '简洁直接,不喜废话,语气坚定,偶尔带有少年意气',
    '原太虚宗外门弟子，因天赋异禀遭同门嫉妒，被诬陷偷盗宗门秘籍而逐出师门',
    '剑眉星目,身材修长,眼神坚毅,左手腕有一道神秘印记',
    '拥有罕见的星辰血脉,能够感知和操控星辰之力',
    '证明自己的清白,重回宗门,最终成为守护苍生的强者'
);
*/

-- =====================================================
-- 7. 权限设置
-- =====================================================

-- 为应用用户授权（根据实际用户名调整）
GRANT SELECT, INSERT, UPDATE, DELETE ON character_profiles TO admin;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO admin;
GRANT SELECT ON protagonist_info TO admin;
GRANT SELECT ON character_stats TO admin;

-- =====================================================
-- 8. 验证脚本
-- =====================================================

-- 检查表是否创建成功
SELECT 
    table_name, 
    table_type 
FROM information_schema.tables 
WHERE table_name IN ('character_profiles') 
    AND table_schema = 'public';

-- 检查索引是否创建成功
SELECT 
    indexname, 
    tablename 
FROM pg_indexes 
WHERE tablename = 'character_profiles';

-- 检查视图是否创建成功
SELECT 
    table_name, 
    table_type 
FROM information_schema.views 
WHERE table_name IN ('protagonist_info', 'character_stats')
    AND table_schema = 'public';

-- 检查worldviews表是否添加了新字段
SELECT 
    column_name, 
    data_type, 
    is_nullable 
FROM information_schema.columns 
WHERE table_name = 'worldviews' 
    AND column_name = 'protagonist_name';

-- =====================================================
-- 执行完成提示
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '==============================================';
    RAISE NOTICE '角色档案系统数据库初始化完成！';
    RAISE NOTICE '==============================================';
    RAISE NOTICE '已创建表：character_profiles';
    RAISE NOTICE '已创建视图：protagonist_info, character_stats';
    RAISE NOTICE '已创建索引：4个索引';
    RAISE NOTICE '已添加字段：worldviews.protagonist_name';
    RAISE NOTICE '==============================================';
    RAISE NOTICE '下一步：';
    RAISE NOTICE '1. 实现CharacterService服务层';
    RAISE NOTICE '2. 修改AI服务集成角色信息';
    RAISE NOTICE '3. 更新章节生成提示词';
    RAISE NOTICE '==============================================';
END $$;
