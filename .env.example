# ===========================================
# InkFlow AI - 环境变量配置示例
# ===========================================
# 复制此文件为 .env 并填入实际值

# 应用基础配置
APP_NAME=InkFlow AI
APP_VERSION=1.0.0
DEBUG=false

# 日志配置
LOG_LEVEL=INFO
SHOW_SQL_LOGS=false

# 数据库配置
DATABASE_URL=postgresql://username:password@localhost:5432/ai_novel

# Redis配置
REDIS_URL=redis://localhost:6379/0

# AI模型配置 - Gemini
GEMINI_API_KEY=your_gemini_api_key_here
GEMINI_MODEL=gemini-2.5-flash

# AI模型配置 - 硅基流动
SILICONFLOW_API_KEY=your_siliconflow_api_key_here
SILICONFLOW_BASE_URL=https://api.siliconflow.cn/v1
SILICONFLOW_MODEL=Qwen/Qwen2.5-7B-Instruct

# LLM配置
DEFAULT_LLM_PROVIDER=siliconflow

# 安全配置
SECRET_KEY=your-super-secret-key-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# API配置
API_PREFIX=/api
CORS_ORIGINS=["*"]

# 内容生成配置
MAX_CHAPTER_LENGTH=3000
MIN_CHAPTER_LENGTH=2000
CHOICES_COUNT=3

# AI创作性配置
CHAPTER_TEMPERATURE=0.9
WORLDVIEW_TEMPERATURE=0.8
SUMMARY_TEMPERATURE=0.3
CHOICES_TEMPERATURE=0.7