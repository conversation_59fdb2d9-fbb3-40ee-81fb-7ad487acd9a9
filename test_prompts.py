#!/usr/bin/env python3
"""
测试优化后的提示词效果（无数据库依赖版本）
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 直接导入提示词类，避免数据库依赖
from app.prompts.unified_prompts import UnifiedPrompts

# 模拟StoryStyle枚举
class StoryStyle:
    XIANXIA = "XIANXIA"

def test_chapter_prompt():
    """测试章节生成提示词"""
    print("=" * 80)
    print("📝 测试优化后的章节生成提示词")
    print("=" * 80)

    # 模拟上下文数据
    context = """
**故事背景信息：**
- 故事标题：仙途独行者
- 故事风格：XIANXIA
- 当前章节：第2章

**世界观设定：**
青云大陆，灵气浓郁的修仙世界。修炼体系包含炼气、筑基、金丹、元婴等境界。

**故事发展脉络：**
第1章：林浩在天元城边缘进入仙灵洞府，与黑衣男子交锋并发现邪灵使者。通过秘籍激发体内力量，击败邪灵使者。章节结尾，林浩站在洞府深处，手持神秘秘籍，决定继续探索。

**用户选择影响：**
**上一章用户选择：** "继续研究仙灵洞府的密籍"

**重要要求：**
- 本章开头必须立即体现这个选择的直接后果
- 选择的影响要在前3句话内明确展现
- 不能忽略或敷衍用户的选择
- 确保选择推动情节发展，保持逻辑连贯性

**当前情境：**
**当前情境：** 林浩站在仙灵洞府深处，手持刚获得的神秘秘籍，周围灵气浓郁，前方还有未探索的通道。

**章节衔接要求：**
- 必须直接承接上章结尾场景，不得重新开始
- 禁止重复已描述的背景信息
- 保持主角位置、状态、情绪的连续性
- 如果上章有未完成的动作，本章必须继续
"""

    # 获取章节生成提示词
    chapter_prompt = UnifiedPrompts.get_chapter_prompt(
        style=StoryStyle.XIANXIA,
        context=context,
        min_words=2500,
        max_words=3000
    )

    print("📋 完整的章节生成提示词：")
    print("=" * 80)
    print(chapter_prompt)
    print("=" * 80)

def test_summary_prompt():
    """测试摘要生成提示词"""
    print("\n" + "=" * 80)
    print("📋 测试摘要生成提示词")
    print("=" * 80)
    
    # 模拟章节内容
    chapter_content = """
    林浩深吸一口气，心中充满决绝之意，踏着洞府曲折的通道，向着洞府深处进发。
    仙灵洞府内隐藏着无数修炼秘籍，是元婴强者留下的宝贵遗产。
    据说洞府深处更有着强者护佑，守护着这些珍贵的秘籍。
    
    经过一番激烈的战斗，林浩最终击败了守护者，获得了珍贵的修炼秘籍。
    他站在密室中，手持秘籍，感受着其中蕴含的强大力量。
    """
    
    # 获取摘要生成提示词
    summary_prompt = UnifiedPrompts.get_summary_prompt(chapter_content)
    
    print("📋 摘要生成提示词：")
    print("-" * 40)
    print(summary_prompt)
    print("-" * 40)

def compare_old_vs_new():
    """对比优化前后的提示词差异"""
    print("\n" + "=" * 80)
    print("🔄 优化前后对比")
    print("=" * 80)
    
    print("✅ 主要改进点：")
    print("1. 增加了强制性衔接要求，确保章节间连贯性")
    print("2. 明确要求体现用户选择的直接后果")
    print("3. 禁止重复背景介绍和突然改变主角位置")
    print("4. 要求在开头3句话内体现选择影响")
    print("5. 增强了摘要的状态描述，为下章提供明确起点")
    
    print("\n🎯 预期效果：")
    print("- 章节间关联词数量应该从0个增加到5个以上")
    print("- 用户选择的影响会在新章节开头立即体现")
    print("- 不再出现重复的背景介绍")
    print("- 主角状态和位置保持连续性")

if __name__ == "__main__":
    test_chapter_prompt()
    test_summary_prompt()
    compare_old_vs_new()
    
    print("\n🚀 提示词优化完成！")
    print("💡 建议：可以用这些优化后的提示词重新生成一个测试故事，对比效果。")
