{"openapi": "3.1.0", "info": {"title": "AI Interactive Novel API", "version": "1.0.0"}, "paths": {"/api/stories/": {"post": {"tags": ["stories"], "summary": "Create Story", "description": "创建新故事", "operationId": "create_story_api_stories__post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateStoryRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StoryResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/stories/{story_id}": {"get": {"tags": ["stories"], "summary": "Get Story", "description": "获取故事详情", "operationId": "get_story_api_stories__story_id__get", "parameters": [{"name": "story_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Story Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StoryResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/stories/{story_id}/chapters": {"get": {"tags": ["stories"], "summary": "Get Story Chapters", "description": "获取故事章节列表", "operationId": "get_story_chapters_api_stories__story_id__chapters_get", "parameters": [{"name": "story_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Story Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["stories"], "summary": "Generate Chapter", "description": "生成新章节", "operationId": "generate_chapter_api_stories__story_id__chapters_post", "parameters": [{"name": "story_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Story Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChapterResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/stories/{story_id}/choices": {"get": {"tags": ["stories"], "summary": "Get Story Choices History", "description": "获取故事选择历史", "operationId": "get_story_choices_history_api_stories__story_id__choices_get", "parameters": [{"name": "story_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Story Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/chapters/{chapter_id}": {"get": {"tags": ["chapters"], "summary": "Get Chapter", "description": "获取章节详情", "operationId": "get_chapter_api_chapters__chapter_id__get", "parameters": [{"name": "chapter_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Chapter Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChapterResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/chapters/{chapter_id}/choices": {"post": {"tags": ["chapters"], "summary": "Submit Choice", "description": "提交用户选择并生成下一章", "operationId": "submit_choice_api_chapters__chapter_id__choices_post", "parameters": [{"name": "chapter_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Chapter Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SubmitChoiceRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NextChapterResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["chapters"], "summary": "Get Chapter Choices", "description": "获取章节的选择选项", "operationId": "get_chapter_choices_api_chapters__chapter_id__choices_get", "parameters": [{"name": "chapter_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Chapter Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/": {"get": {"summary": "Root", "description": "根路径", "operationId": "root__get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/health": {"get": {"summary": "Health Check", "description": "健康检查", "operationId": "health_check_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}}, "components": {"schemas": {"ChapterResponse": {"properties": {"success": {"type": "boolean", "title": "Success"}, "data": {"additionalProperties": true, "type": "object", "title": "Data"}, "message": {"type": "string", "title": "Message", "default": ""}}, "type": "object", "required": ["success", "data"], "title": "ChapterResponse"}, "CreateStoryRequest": {"properties": {"style": {"$ref": "#/components/schemas/StoryStyle"}, "title": {"type": "string", "title": "Title"}}, "type": "object", "required": ["style"], "title": "CreateStoryRequest"}, "HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "NextChapterResponse": {"properties": {"success": {"type": "boolean", "title": "Success"}, "data": {"additionalProperties": true, "type": "object", "title": "Data"}, "message": {"type": "string", "title": "Message", "default": ""}}, "type": "object", "required": ["success", "data"], "title": "NextChapterResponse"}, "StoryResponse": {"properties": {"success": {"type": "boolean", "title": "Success"}, "data": {"additionalProperties": true, "type": "object", "title": "Data"}, "message": {"type": "string", "title": "Message", "default": ""}}, "type": "object", "required": ["success", "data"], "title": "StoryResponse"}, "StoryStyle": {"type": "string", "enum": ["修仙", "武侠", "科技"], "title": "StoryStyle"}, "SubmitChoiceRequest": {"properties": {"choice_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Choice Id"}, "custom_choice": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Custom Choice"}}, "type": "object", "title": "SubmitChoiceRequest"}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}}}}