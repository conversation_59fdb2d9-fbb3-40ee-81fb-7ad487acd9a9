[project]
name = "inkflow-ai"
version = "0.1.0"
description = "AI Interactive Novel Platform - Backend API"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    # Web框架
    "fastapi>=0.104.1",
    "uvicorn[standard]>=0.24.0",
    "fastapi-cors>=0.0.6",
    # 数据库
    "sqlalchemy>=2.0.23",
    "psycopg2-binary>=2.9.9",
    "alembic>=1.12.1",
    # 缓存
    "redis>=5.0.0",
    # 数据验证
    "pydantic>=2.7.4",
    "pydantic-settings>=2.0.0",
    # 环境配置
    "python-dotenv>=1.0.0",
    # 安全
    "python-jose[cryptography]>=3.3.0",
    "python-multipart>=0.0.6",
    "PyJWT>=2.8.0",
    # 日志
    "loguru>=0.7.2",
    # 开发和测试
    "pytest>=7.4.3",
    "pytest-asyncio>=0.21.1",
    "httpx>=0.25.2",
    "requests>=2.31.0",
    "google-genai>=1.27.0",
    "openai>=1.97.1",
    "bcrypt>=4.3.0",
    "aiohttp>=3.12.14",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.3",
    "pytest-asyncio>=0.21.1",
    "httpx>=0.25.2",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["app"]

[tool.uv]
dev-dependencies = [
    "pytest>=7.4.3",
    "pytest-asyncio>=0.21.1",
    "httpx>=0.25.2",
]

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
asyncio_mode = "auto"
addopts = "-v --tb=short"

[tool.uvicorn]
app = "main:app"
host = "0.0.0.0"
port = 20001
reload = true
