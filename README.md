# InkFlow AI - AI驱动的交互式小说平台

🎭 基于AI的交互式小说创作平台，让每个人都能成为故事的主角。

## ✨ 项目简介

InkFlow AI 是一个创新的AI驱动交互式小说平台，用户可以：
- 🎨 创建个性化的AI小说
- 🎯 在关键节点做出选择，影响故事走向
- 🤖 体验由先进AI模型生成的高质量内容
- 📚 支持多种小说风格：修仙、都市、科幻、奇幻等

## 🚀 技术栈

- **后端框架**: FastAPI
- **数据库**: PostgreSQL + Redis
- **AI模型**: 硅基流动 (Qwen2.5) + Google Gemini
- **认证**: JWT
- **部署**: Docker + Docker Compose
- **开发工具**: uv (现代Python包管理器)

## 📁 项目结构

```
inkflow-ai/
├── app/
│   ├── api/           # API路由层
│   ├── config/        # 配置管理
│   ├── database/      # 数据库连接
│   ├── models/        # 数据模型
│   ├── services/      # 业务逻辑层
│   │   └── llm/       # LLM抽象层
│   └── utils/         # 工具函数
├── scripts/           # 部署脚本
├── docs/             # 项目文档
├── tests/            # 测试文件
└── docker-compose.yml # 生产部署配置
```

## 🚀 快速部署

### 系统要求

- **操作系统**: Linux (推荐Ubuntu 20.04+)
- **Docker**: 20.10+
- **Docker Compose**: 2.0+
- **内存**: 4GB+
- **存储**: 20GB+

### 一键部署

1. **克隆项目**
```bash
git clone <your-repository-url>
cd inkflow-ai
```

2. **配置环境**
```bash
# 复制环境配置模板
cp .env.example .env

# 编辑配置文件
nano .env
```

3. **部署服务**
```bash
# 使用部署脚本
./scripts/deploy.sh

# 或使用Makefile
make deploy
```

4. **验证部署**
```bash
# 检查服务状态
make status

# 检查健康状态
make health
```

### 环境配置

编辑 `.env` 文件，配置以下关键参数：

```bash
# AI模型配置 (必需)
GEMINI_API_KEY=your_gemini_api_key_here
SILICONFLOW_API_KEY=your_siliconflow_api_key_here

# 数据库配置 (必需)
DATABASE_URL=*********************************************/ai_novel

# 安全配置 (必需)
SECRET_KEY=your-super-secret-key-here

# 其他配置
DEFAULT_LLM_PROVIDER=siliconflow
DEBUG=false
```

## 🛠️ 管理命令

项目提供了便捷的管理命令：

```bash
# 查看所有可用命令
make help

# 部署和管理
make deploy      # 部署生产环境
make start       # 启动服务
make stop        # 停止服务
make restart     # 重启服务
make status      # 查看服务状态
make logs        # 查看日志
make health      # 检查健康状态
make update      # 更新服务
make backup      # 备份数据

# 本地开发
make install     # 安装依赖
make dev         # 开发模式启动
make test        # 运行测试
make clean       # 清理缓存
```

## 📚 API文档

启动服务后，可以通过以下地址访问API文档：

- **Swagger UI**: http://localhost:20001/docs
- **ReDoc**: http://localhost:20001/redoc
- **健康检查**: http://localhost:20001/health

## 🎯 核心功能

### 用户认证
- 用户注册和登录
- JWT令牌管理
- 安全的身份验证

### 故事管理
- 创建个性化故事
- 多种故事风格支持
- 智能世界观生成

### 章节生成
- AI驱动的内容生成
- 实时流式传输
- 智能选择分支
- 故事连贯性保证

### AI模型支持
- **硅基流动**: 免费的国产大模型，中文理解能力强
- **Google Gemini**: 稳定的多语言模型
- 智能模型切换和负载均衡

## 🔧 本地开发

如果需要本地开发，可以使用以下方式：

### 使用uv (推荐)

```bash
# 安装uv
curl -LsSf https://astral.sh/uv/install.sh | sh

# 安装依赖
make install

# 启动开发服务器
make dev
```

### 使用传统方式

```bash
# 创建虚拟环境
python -m venv venv
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt

# 启动服务器
uvicorn main:app --host 0.0.0.0 --port 20001 --reload
```

## 📖 文档

- [生产部署指南](docs/生产部署指南.md)
- [API接口文档](API_ENDPOINTS.md)
- [技术架构设计](docs/AI交互式小说产品-技术架构设计.md)

## 🔒 安全配置

- JWT令牌认证
- 环境变量管理
- CORS跨域控制
- 输入验证和过滤

## 🚀 性能特性

- 异步处理架构
- 流式内容传输
- Redis缓存优化
- 数据库连接池

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🆘 支持

如果遇到问题，请：

1. 查看 [生产部署指南](docs/生产部署指南.md)
2. 检查服务日志: `make logs`
3. 验证健康状态: `make health`
4. 提交 Issue 或联系维护者

---

🌟 **InkFlow AI** - 让AI为您编织无限可能的故事世界！
