#!/usr/bin/env python3
"""
查询指定ID的小说内容和章节信息
"""
import sqlite3
import json
import sys
from typing import List, Dict, Any

def check_database_structure():
    """检查数据库结构"""
    db_path = "ai_novel.db"

    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # 获取所有表名
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = [row[0] for row in cursor.fetchall()]

        print("📋 数据库表结构:")
        for table in tables:
            print(f"\n🔸 表: {table}")
            cursor.execute(f"PRAGMA table_info({table});")
            columns = cursor.fetchall()
            for col in columns:
                print(f"   - {col[1]} ({col[2]})")

        conn.close()
        return tables

    except Exception as e:
        print(f"❌ 检查数据库结构失败: {str(e)}")
        return []

def query_story_content(story_id: str) -> Dict[str, Any]:
    """查询指定ID的小说完整内容"""
    db_path = "ai_novel.db"

    try:
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row  # 使结果可以通过列名访问
        cursor = conn.cursor()

        # 查询故事基本信息
        cursor.execute("""
            SELECT id, title, style, status, current_chapter_number,
                   created_at, updated_at, state_data, chapter_summaries, character_info
            FROM stories
            WHERE id = ?
        """, (story_id,))

        story_row = cursor.fetchone()
        if not story_row:
            return {"error": f"未找到ID为 {story_id} 的故事"}

        story_info = dict(story_row)

        # 查询世界观信息 - 先检查表是否存在
        worldview_info = None
        try:
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='worldviews';")
            if cursor.fetchone():
                # 获取worldviews表的列信息
                cursor.execute("PRAGMA table_info(worldviews);")
                columns = [col[1] for col in cursor.fetchall()]

                # 构建动态查询
                column_list = ", ".join(columns)
                cursor.execute(f"SELECT {column_list} FROM worldviews WHERE story_id = ?", (story_id,))
                worldview_row = cursor.fetchone()
                worldview_info = dict(worldview_row) if worldview_row else None
        except Exception as e:
            print(f"⚠️ 查询世界观信息失败: {e}")
            worldview_info = None
        
        # 查询所有章节
        cursor.execute("""
            SELECT id, chapter_number, title, content, summary, created_at
            FROM chapters 
            WHERE story_id = ? 
            ORDER BY chapter_number
        """, (story_id,))
        
        chapters = []
        for chapter_row in cursor.fetchall():
            chapter_info = dict(chapter_row)
            chapter_id = chapter_info['id']
            
            # 查询该章节的选择选项
            cursor.execute("""
                SELECT id, choice_text, choice_type, is_selected, created_at
                FROM choices 
                WHERE chapter_id = ?
                ORDER BY created_at
            """, (chapter_id,))
            
            choices = [dict(choice_row) for choice_row in cursor.fetchall()]
            chapter_info['choices'] = choices
            chapters.append(chapter_info)
        
        conn.close()
        
        return {
            "story": story_info,
            "worldview": worldview_info,
            "chapters": chapters,
            "total_chapters": len(chapters)
        }
        
    except Exception as e:
        return {"error": f"查询失败: {str(e)}"}

def print_story_analysis(data: Dict[str, Any]):
    """打印故事分析结果"""
    if "error" in data:
        print(f"❌ {data['error']}")
        return
    
    story = data["story"]
    worldview = data["worldview"]
    chapters = data["chapters"]
    
    print("=" * 80)
    print(f"📚 故事分析报告")
    print("=" * 80)
    
    # 基本信息
    print(f"\n📖 故事基本信息:")
    print(f"   标题: {story['title']}")
    print(f"   风格: {story['style']}")
    print(f"   状态: {story['status']}")
    print(f"   当前章节数: {story['current_chapter_number']}")
    print(f"   创建时间: {story['created_at']}")
    
    # 世界观信息
    if worldview:
        print(f"\n🌍 世界观设定:")
        print(f"   世界名称: {worldview['world_name']}")
        print(f"   故事主题: {worldview['story_theme']}")
        print(f"   主角设定: {worldview['main_character'][:100]}...")
        print(f"   核心冲突: {worldview['conflict_setup'][:100]}...")
    
    # 章节分析
    print(f"\n📝 章节分析 (共{len(chapters)}章):")
    
    for i, chapter in enumerate(chapters):
        print(f"\n--- 第{chapter['chapter_number']}章: {chapter['title']} ---")
        
        # 内容长度分析
        content_length = len(chapter['content'])
        print(f"   📏 内容长度: {content_length} 字")
        
        # 摘要
        if chapter['summary']:
            print(f"   📋 摘要: {chapter['summary'][:150]}...")
        
        # 内容预览
        content_preview = chapter['content'][:200].replace('\n', ' ')
        print(f"   👀 内容预览: {content_preview}...")
        
        # 选择选项分析
        choices = chapter['choices']
        print(f"   🎯 选择选项 ({len(choices)}个):")
        for j, choice in enumerate(choices, 1):
            selected_mark = "✅" if choice['is_selected'] else "⭕"
            print(f"      {j}. {selected_mark} {choice['choice_text']}")
        
        # 章节间关联性分析
        if i > 0:
            prev_chapter = chapters[i-1]
            # 简单的关联性检查：看是否有共同的关键词
            current_words = set(chapter['content'][:500].split())
            prev_words = set(prev_chapter['content'][-500:].split())
            common_words = current_words.intersection(prev_words)
            common_words = {w for w in common_words if len(w) > 2}  # 过滤短词
            
            print(f"   🔗 与上章关联词: {len(common_words)}个 - {list(common_words)[:5]}")
    
    # 整体质量分析
    print(f"\n📊 整体质量分析:")
    
    # 字数分析
    total_words = sum(len(ch['content']) for ch in chapters)
    avg_words = total_words / len(chapters) if chapters else 0
    print(f"   📏 总字数: {total_words} 字")
    print(f"   📏 平均章节长度: {avg_words:.0f} 字")
    
    # 字数分布
    word_counts = [len(ch['content']) for ch in chapters]
    min_words = min(word_counts) if word_counts else 0
    max_words = max(word_counts) if word_counts else 0
    print(f"   📏 章节长度范围: {min_words} - {max_words} 字")
    
    # 选择选项分析
    total_choices = sum(len(ch['choices']) for ch in chapters)
    avg_choices = total_choices / len(chapters) if chapters else 0
    print(f"   🎯 总选择选项: {total_choices} 个")
    print(f"   🎯 平均每章选项: {avg_choices:.1f} 个")
    
    # 用户选择分析
    selected_choices = []
    for ch in chapters:
        for choice in ch['choices']:
            if choice['is_selected']:
                selected_choices.append(choice['choice_text'])
    
    print(f"   ✅ 用户已选择: {len(selected_choices)} 个")
    if selected_choices:
        print(f"   ✅ 选择历史: {selected_choices}")

def list_all_stories():
    """列出所有故事"""
    db_path = "ai_novel.db"

    try:
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        cursor.execute("""
            SELECT id, title, style, status, current_chapter_number, created_at
            FROM stories
            ORDER BY created_at DESC
        """)

        stories = [dict(row) for row in cursor.fetchall()]
        conn.close()

        print("📚 数据库中的所有故事:")
        print("=" * 80)

        if not stories:
            print("❌ 数据库中没有故事")
            return None

        for i, story in enumerate(stories, 1):
            print(f"{i}. ID: {story['id']}")
            print(f"   标题: {story['title']}")
            print(f"   风格: {story['style']}")
            print(f"   章节数: {story['current_chapter_number']}")
            print(f"   创建时间: {story['created_at']}")
            print("-" * 40)

        return stories[0]['id'] if stories else None

    except Exception as e:
        print(f"❌ 查询失败: {str(e)}")
        return None

if __name__ == "__main__":
    if len(sys.argv) > 1:
        if sys.argv[1] == "--check-db":
            check_database_structure()
        else:
            story_id = sys.argv[1]
            print(f"🔍 正在查询故事ID: {story_id}")
            data = query_story_content(story_id)
            print_story_analysis(data)
    else:
        # 先列出所有故事
        first_story_id = list_all_stories()

        if first_story_id:
            print(f"\n🔍 正在分析第一个故事: {first_story_id}")
            data = query_story_content(first_story_id)
            print_story_analysis(data)
