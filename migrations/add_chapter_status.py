"""
数据库迁移脚本：为Chapter表添加status和updated_at字段
实现后台继续生成策略，解决用户中断问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import create_engine, text
from app.config import settings
from app.database import get_db
from app.models import Chapter, ChapterStatus
from app.utils.logger import get_logger

logger = get_logger(__name__)

def add_chapter_status_fields():
    """为Chapter表添加status和updated_at字段"""
    try:
        # 创建数据库引擎
        engine = create_engine(settings.database_url)
        
        with engine.connect() as conn:
            # 检查status字段是否已存在
            result = conn.execute(text("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'chapters' AND column_name = 'status'
            """))
            
            if result.fetchone():
                logger.info("status字段已存在，跳过添加")
            else:
                # 添加status字段
                conn.execute(text("""
                    ALTER TABLE chapters 
                    ADD COLUMN status VARCHAR(20) DEFAULT 'complete'
                """))
                logger.info("✅ status字段添加成功")
            
            # 检查updated_at字段是否已存在
            result = conn.execute(text("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'chapters' AND column_name = 'updated_at'
            """))
            
            if result.fetchone():
                logger.info("updated_at字段已存在，跳过添加")
            else:
                # 添加updated_at字段
                conn.execute(text("""
                    ALTER TABLE chapters 
                    ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                """))
                logger.info("✅ updated_at字段添加成功")
            
            # 创建索引以提高查询性能
            conn.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_chapters_story_status 
                ON chapters(story_id, status)
            """))
            
            conn.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_chapters_status_updated 
                ON chapters(status, updated_at)
            """))
            
            conn.commit()
            logger.info("✅ 索引创建成功")
            return True
            
    except Exception as e:
        logger.error(f"❌ 添加章节状态字段失败: {e}")
        return False

def migrate_existing_chapters():
    """迁移现有的章节数据"""
    try:
        db = next(get_db())
        
        # 将所有现有章节标记为complete（因为它们已经完成）
        complete_count = db.execute(text("""
            UPDATE chapters 
            SET status = 'complete', updated_at = CURRENT_TIMESTAMP
            WHERE status IS NULL OR status = 'complete'
        """)).rowcount
        
        db.commit()
        
        logger.info(f"✅ 数据迁移完成:")
        logger.info(f"  - {complete_count} 个章节标记为complete")
        
        return complete_count
        
    except Exception as e:
        db.rollback()
        logger.error(f"❌ 数据迁移失败: {e}")
        return 0

def verify_migration():
    """验证迁移结果"""
    try:
        db = next(get_db())
        
        # 统计各种状态的章节
        generating_count = db.execute(text("""
            SELECT COUNT(*) FROM chapters WHERE status = 'generating'
        """)).scalar()
        
        content_ready_count = db.execute(text("""
            SELECT COUNT(*) FROM chapters WHERE status = 'content_ready'
        """)).scalar()
        
        complete_count = db.execute(text("""
            SELECT COUNT(*) FROM chapters WHERE status = 'complete'
        """)).scalar()
        
        total_count = db.execute(text("""
            SELECT COUNT(*) FROM chapters
        """)).scalar()
        
        logger.info(f"📊 迁移验证结果:")
        logger.info(f"  总章节数: {total_count}")
        logger.info(f"  生成中: {generating_count}")
        logger.info(f"  内容就绪: {content_ready_count}")
        logger.info(f"  已完成: {complete_count}")
        
        # 检查是否有章节缺少状态
        null_status_count = db.execute(text("""
            SELECT COUNT(*) FROM chapters WHERE status IS NULL
        """)).scalar()
        
        if null_status_count > 0:
            logger.warning(f"⚠️  发现 {null_status_count} 个章节缺少状态")
        else:
            logger.info("✅ 所有章节都有正确的状态")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 迁移验证失败: {e}")
        return False

def main():
    """主迁移流程"""
    print("🚀 开始Chapter表状态字段迁移")
    print("=" * 60)
    
    # 步骤1：添加字段
    print("📋 步骤1：添加status和updated_at字段...")
    if not add_chapter_status_fields():
        print("❌ 字段添加失败，迁移终止")
        return False
    
    # 步骤2：迁移数据
    print("\n📋 步骤2：迁移现有章节数据...")
    migrated_count = migrate_existing_chapters()
    if migrated_count == 0:
        print("⚠️  没有数据需要迁移或迁移失败")
    
    # 步骤3：验证结果
    print("\n📋 步骤3：验证迁移结果...")
    if not verify_migration():
        print("❌ 迁移验证失败")
        return False
    
    print("\n🎉 Chapter表状态字段迁移完成！")
    print("📋 新增功能:")
    print("  ✅ 后台继续生成策略")
    print("  ✅ 章节状态管理")
    print("  ✅ 用户中断恢复")
    print("  ✅ 生成进度追踪")
    print("  ✅ 数据库性能优化")
    
    print("\n💡 使用说明:")
    print("  - generating: 章节正在后台生成中")
    print("  - content_ready: 内容已生成，等待选项")
    print("  - complete: 章节完全生成完成")
    print("  - 用户可随时查询生成状态")
    print("  - 后端会继续执行直到完成")
    
    print("\n🎯 解决的问题:")
    print("  ✅ 用户生成过程中退出不会丢失进度")
    print("  ✅ 网络不稳定不影响生成")
    print("  ✅ 用户回来可以直接获取结果")
    print("  ✅ 避免重复生成浪费资源")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
