"""
数据库迁移脚本：为Choice表添加choice_status字段
实现混合存储策略，解决用户会话连续性问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import create_engine, text
from app.config import settings
from app.database import get_db
from app.models import Choice, ChoiceStatus
from app.utils.logger import get_logger

logger = get_logger(__name__)

def add_choice_status_column():
    """为Choice表添加choice_status字段"""
    try:
        # 创建数据库引擎
        engine = create_engine(settings.database_url)
        
        with engine.connect() as conn:
            # 检查字段是否已存在
            result = conn.execute(text("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'choices' AND column_name = 'choice_status'
            """))
            
            if result.fetchone():
                logger.info("choice_status字段已存在，跳过添加")
                return True
            
            # 添加choice_status字段
            conn.execute(text("""
                ALTER TABLE choices 
                ADD COLUMN choice_status VARCHAR(20) DEFAULT 'temporary'
            """))
            
            # 创建索引以提高查询性能
            conn.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_choices_status_chapter 
                ON choices(chapter_id, choice_status)
            """))
            
            conn.commit()
            logger.info("✅ choice_status字段添加成功")
            return True
            
    except Exception as e:
        logger.error(f"❌ 添加choice_status字段失败: {e}")
        return False

def migrate_existing_choices():
    """迁移现有的选择数据"""
    try:
        db = next(get_db())
        
        # 将所有已选择的选项标记为final
        final_count = db.execute(text("""
            UPDATE choices 
            SET choice_status = 'final' 
            WHERE is_selected = true
        """)).rowcount
        
        # 将所有未选择的选项标记为temporary（实际上应该删除，但先保留）
        temp_count = db.execute(text("""
            UPDATE choices 
            SET choice_status = 'temporary' 
            WHERE is_selected = false
        """)).rowcount
        
        db.commit()
        
        logger.info(f"✅ 数据迁移完成:")
        logger.info(f"  - {final_count} 个选择标记为final")
        logger.info(f"  - {temp_count} 个选择标记为temporary")
        
        return final_count + temp_count
        
    except Exception as e:
        db.rollback()
        logger.error(f"❌ 数据迁移失败: {e}")
        return 0

def cleanup_redundant_choices():
    """清理冗余的选择数据"""
    try:
        db = next(get_db())
        
        # 对于每个章节，只保留一个final选择，删除多余的temporary选择
        # 但保留最近生成的temporary选择（以防用户还在选择中）
        
        # 删除超过24小时的temporary选择
        from datetime import datetime, timedelta
        cutoff_time = datetime.now() - timedelta(hours=24)
        
        deleted_count = db.execute(text("""
            DELETE FROM choices 
            WHERE choice_status = 'temporary' 
            AND created_at < :cutoff_time
        """), {"cutoff_time": cutoff_time}).rowcount
        
        db.commit()
        
        logger.info(f"✅ 清理了 {deleted_count} 个过期的临时选择")
        return deleted_count
        
    except Exception as e:
        db.rollback()
        logger.error(f"❌ 清理冗余选择失败: {e}")
        return 0

def verify_migration():
    """验证迁移结果"""
    try:
        db = next(get_db())
        
        # 统计各种状态的选择
        final_count = db.execute(text("""
            SELECT COUNT(*) FROM choices WHERE choice_status = 'final'
        """)).scalar()
        
        temp_count = db.execute(text("""
            SELECT COUNT(*) FROM choices WHERE choice_status = 'temporary'
        """)).scalar()
        
        total_count = db.execute(text("""
            SELECT COUNT(*) FROM choices
        """)).scalar()
        
        logger.info(f"📊 迁移验证结果:")
        logger.info(f"  总选择数: {total_count}")
        logger.info(f"  最终选择: {final_count}")
        logger.info(f"  临时选择: {temp_count}")
        
        # 检查是否有章节有多个final选择（不应该存在）
        duplicate_finals = db.execute(text("""
            SELECT chapter_id, COUNT(*) as count
            FROM choices 
            WHERE choice_status = 'final' 
            GROUP BY chapter_id 
            HAVING COUNT(*) > 1
        """)).fetchall()
        
        if duplicate_finals:
            logger.warning(f"⚠️  发现 {len(duplicate_finals)} 个章节有多个final选择")
            for chapter_id, count in duplicate_finals:
                logger.warning(f"  章节 {chapter_id}: {count} 个final选择")
        else:
            logger.info("✅ 没有发现重复的final选择")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 迁移验证失败: {e}")
        return False

def main():
    """主迁移流程"""
    print("🚀 开始Choice表状态字段迁移")
    print("=" * 60)
    
    # 步骤1：添加字段
    print("📋 步骤1：添加choice_status字段...")
    if not add_choice_status_column():
        print("❌ 字段添加失败，迁移终止")
        return False
    
    # 步骤2：迁移数据
    print("\n📋 步骤2：迁移现有选择数据...")
    migrated_count = migrate_existing_choices()
    if migrated_count == 0:
        print("⚠️  没有数据需要迁移或迁移失败")
    
    # 步骤3：清理冗余数据
    print("\n📋 步骤3：清理冗余选择数据...")
    cleaned_count = cleanup_redundant_choices()
    
    # 步骤4：验证结果
    print("\n📋 步骤4：验证迁移结果...")
    if not verify_migration():
        print("❌ 迁移验证失败")
        return False
    
    print("\n🎉 Choice表状态字段迁移完成！")
    print("📋 新增功能:")
    print("  ✅ 混合存储策略")
    print("  ✅ 用户会话连续性")
    print("  ✅ 临时选项自动清理")
    print("  ✅ 数据库性能优化")
    print("  ✅ 用户体验改善")
    
    print("\n💡 使用说明:")
    print("  - 选项生成后以temporary状态存储")
    print("  - 用户选择后升级为final状态")
    print("  - 临时选项24小时后自动清理")
    print("  - 用户退出后可恢复待选择的选项")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
