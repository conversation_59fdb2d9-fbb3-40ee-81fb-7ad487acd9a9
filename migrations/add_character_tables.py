"""
数据库迁移脚本：添加独立的角色数据表
执行此脚本将创建新的角色相关表，并迁移现有的角色数据
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import create_engine, text
from app.config import settings
from app.database import get_db
from app.models import Character, CharacterRelationship, CharacterEvent, Story
from app.utils.logger import get_logger
import uuid

logger = get_logger(__name__)

def create_character_tables():
    """创建角色相关表"""
    try:
        # 创建数据库引擎
        engine = create_engine(settings.database_url)
        
        # 创建角色表
        Character.__table__.create(engine, checkfirst=True)
        logger.info("✅ 角色表创建成功")
        
        # 创建角色关系表
        CharacterRelationship.__table__.create(engine, checkfirst=True)
        logger.info("✅ 角色关系表创建成功")
        
        # 创建角色事件表
        CharacterEvent.__table__.create(engine, checkfirst=True)
        logger.info("✅ 角色事件表创建成功")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 创建角色表失败: {e}")
        return False

def migrate_existing_character_data():
    """迁移现有的角色数据"""
    try:
        db = next(get_db())
        migrated_count = 0
        
        # 查询所有有角色信息的故事
        stories_with_characters = db.query(Story).filter(
            Story.character_info.isnot(None)
        ).all()
        
        logger.info(f"发现 {len(stories_with_characters)} 个故事包含角色信息")
        
        for story in stories_with_characters:
            try:
                character_info = story.character_info
                if not character_info or not character_info.get('name'):
                    continue
                
                # 检查是否已经迁移过
                existing_character = db.query(Character).filter(
                    Character.story_id == story.id,
                    Character.role_type == 'protagonist'
                ).first()
                
                if existing_character:
                    logger.info(f"故事 {story.title} 的角色已存在，跳过迁移")
                    continue
                
                # 创建新的角色记录
                character = Character(
                    story_id=story.id,
                    name=character_info.get('name', '未命名角色'),
                    role_type='protagonist',
                    personality_traits=character_info.get('personality_traits', ''),
                    speaking_style=character_info.get('speaking_style', ''),
                    background=character_info.get('background', ''),
                    appearance=character_info.get('appearance', ''),
                    core_abilities=character_info.get('core_abilities', ''),
                    growth_goals=character_info.get('growth_goals', ''),
                    key_relationships=character_info.get('key_relationships', ''),
                    current_state=character_info.get('current_state', '故事开始'),
                    current_location='起始地点',
                    current_mood='平静',
                    health_status='良好',
                    power_level=1,
                    character_arc_stage='初期'
                )
                
                db.add(character)
                migrated_count += 1
                
                logger.info(f"✅ 迁移角色: {character.name} (故事: {story.title})")
                
            except Exception as e:
                logger.error(f"❌ 迁移故事 {story.title} 的角色失败: {e}")
                continue
        
        # 提交所有更改
        db.commit()
        logger.info(f"🎉 角色数据迁移完成，共迁移 {migrated_count} 个角色")
        
        return migrated_count
        
    except Exception as e:
        db.rollback()
        logger.error(f"❌ 角色数据迁移失败: {e}")
        return 0

def verify_migration():
    """验证迁移结果"""
    try:
        db = next(get_db())
        
        # 统计角色数量
        character_count = db.query(Character).count()
        protagonist_count = db.query(Character).filter(Character.role_type == 'protagonist').count()
        
        logger.info(f"📊 迁移验证结果:")
        logger.info(f"  总角色数: {character_count}")
        logger.info(f"  主角数: {protagonist_count}")
        
        # 显示一些示例
        sample_characters = db.query(Character).limit(3).all()
        for char in sample_characters:
            logger.info(f"  示例角色: {char.name} (故事ID: {char.story_id})")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 迁移验证失败: {e}")
        return False

def main():
    """主迁移流程"""
    print("🚀 开始角色数据表迁移")
    print("=" * 60)
    
    # 步骤1：创建表
    print("📋 步骤1：创建角色相关表...")
    if not create_character_tables():
        print("❌ 表创建失败，迁移终止")
        return False
    
    # 步骤2：迁移数据
    print("\n📋 步骤2：迁移现有角色数据...")
    migrated_count = migrate_existing_character_data()
    if migrated_count == 0:
        print("⚠️  没有数据需要迁移或迁移失败")
    
    # 步骤3：验证结果
    print("\n📋 步骤3：验证迁移结果...")
    if not verify_migration():
        print("❌ 迁移验证失败")
        return False
    
    print("\n🎉 角色数据表迁移完成！")
    print("📋 新增功能:")
    print("  ✅ 独立的角色数据表")
    print("  ✅ 动态角色状态管理")
    print("  ✅ 角色关系系统")
    print("  ✅ 角色事件记录")
    print("  ✅ 现有数据完整迁移")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
