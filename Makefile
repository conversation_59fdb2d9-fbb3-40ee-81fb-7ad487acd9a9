.PHONY: help install dev test clean run deploy start stop restart status logs health update clean-docker backup

# 默认目标
help:
	@echo "InkFlow AI - 可用命令:"
	@echo ""
	@echo "本地开发:"
	@echo "  install     - 安装依赖"
	@echo "  dev         - 本地开发模式启动服务器"
	@echo "  test        - 运行测试"
	@echo "  clean       - 清理缓存文件"
	@echo "  run         - 生产模式启动服务器"
	@echo ""
	@echo "生产部署:"
	@echo "  deploy      - 部署生产环境"
	@echo "  start       - 启动服务"
	@echo "  stop        - 停止服务"
	@echo "  restart     - 重启服务"
	@echo "  status      - 查看服务状态"
	@echo "  logs        - 查看日志"
	@echo "  health      - 检查健康状态"
	@echo "  update      - 更新服务"
	@echo "  clean-docker - 清理Docker环境"
	@echo "  backup      - 备份数据"

# 安装依赖
install:
	uv sync

# 开发模式启动
dev:
	uv run uvicorn main:app --host 0.0.0.0 --port 20001 --reload

# 运行测试
test:
	uv run pytest tests/ -v

# 清理缓存
clean:
	find . -type d -name "__pycache__" -exec rm -rf {} +
	find . -type f -name "*.pyc" -delete
	find . -type f -name "*.pyo" -delete
	find . -type d -name ".pytest_cache" -exec rm -rf {} +

# 生产模式启动
run:
	uv run uvicorn main:app --host 0.0.0.0 --port 20001

# ===== 生产部署命令 =====

# 部署生产环境
deploy:
	@echo "🚀 部署生产环境..."
	./scripts/manage.sh deploy

# 启动服务
start:
	@echo "🚀 启动服务..."
	./scripts/manage.sh start

# 停止服务
stop:
	@echo "🛑 停止服务..."
	./scripts/manage.sh stop

# 重启服务
restart:
	@echo "🔄 重启服务..."
	./scripts/manage.sh restart

# 查看服务状态
status:
	@echo "📊 查看服务状态..."
	./scripts/manage.sh status

# 查看日志
logs:
	@echo "📋 查看日志..."
	./scripts/manage.sh logs

# 检查健康状态
health:
	@echo "🔍 检查健康状态..."
	./scripts/manage.sh health

# 更新服务
update:
	@echo "🔄 更新服务..."
	./scripts/manage.sh update

# 清理Docker环境
clean-docker:
	@echo "🧹 清理Docker环境..."
	./scripts/manage.sh clean

# 备份数据
backup:
	@echo "💾 备份数据..."
	./scripts/manage.sh backup
