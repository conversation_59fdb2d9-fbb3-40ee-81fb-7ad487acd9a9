from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List, Dict, Any
from pydantic import BaseModel

from app.database import get_db
from app.models import StoryStyle, User
from app.models.responses import (
    SuccessResponse, ErrorResponse, StoriesListResponse,
    StoryDetail, StoryResponse as StoryResponseModel, STANDARD_RESPONSES,
    ChaptersListResponse, ChapterDetail, StoryChoicesHistoryResponse
)
from app.services import StoryService
from app.utils.logger import get_logger
from .auth import get_current_user

logger = get_logger(__name__)

router = APIRouter(prefix="/stories", tags=["故事"])

# Pydantic模型
class CreateStoryRequest(BaseModel):
    style: StoryStyle
    title: str = None  # 可选，如果不提供则AI自动生成
    provider: str = None  # LLM服务商：siliconflow, openrouter, gemini

class StoryResponse(BaseModel):
    success: bool
    data: Dict[str, Any]
    message: str = ""



@router.get("/",
           response_model=StoriesListResponse,
           responses=STANDARD_RESPONSES)
async def get_all_stories(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> StoriesListResponse:
    """获取所有故事列表"""
    try:
        story_service = StoryService(db)
        stories = story_service.get_user_stories(current_user.id)

        # 转换为详细故事模型
        story_details = []
        for story in stories:
            # 获取章节数量
            chapters = story_service.get_story_chapters(story.id)
            story_detail = StoryDetail(
                id=story.id,
                title=story.title,
                genre=story.style.value if story.style else "",  # 使用style作为genre
                style=story.style.value if story.style else "",
                description=getattr(story, 'description', ''),
                status=story.status.value if story.status else "active",
                current_chapter_number=story.current_chapter_number,
                chapter_count=len(chapters),
                created_at=story.created_at,
                updated_at=story.updated_at
            )
            story_details.append(story_detail)

        return StoriesListResponse.create(
            stories=story_details,
            total=len(story_details)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取故事列表失败: {str(e)}"
        )

@router.post("/",
            response_model=SuccessResponse,
            responses=STANDARD_RESPONSES)
async def create_story(
    request: CreateStoryRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> SuccessResponse:
    """创建新故事并生成世界观框架"""
    try:
        story_service = StoryService(db)

        # 使用智能创建流程：AI自动生成标题和世界观
        result = await story_service.create_story_with_ai_generation(
            style=request.style,
            title=request.title,  # 可选，如果为空则AI生成
            user_id=current_user.id,
            provider=request.provider
        )

        return SuccessResponse(
            data=result,
            message="故事创建成功，标题和世界观已由AI生成"
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建故事失败: {str(e)}"
        )

@router.get("/{story_id}", response_model=StoryResponse)
async def get_story(
    story_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取故事详情"""
    try:
        story_service = StoryService(db)
        story = story_service.get_story(story_id)
        
        if not story:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="故事不存在"
            )
        
        # 检查用户权限
        if story.user_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问此故事"
            )
        
        return StoryResponse(
            success=True,
            data=story.to_dict()
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取故事失败: {str(e)}"
        )

@router.get("/{story_id}/chapters",
           response_model=ChaptersListResponse,
           responses=STANDARD_RESPONSES)
async def get_story_chapters(
    story_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> ChaptersListResponse:
    """获取故事章节列表"""
    try:
        story_service = StoryService(db)
        story = story_service.get_story(story_id)

        if not story:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="故事不存在"
            )

        # 检查用户权限
        if story.user_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问此故事"
            )

        chapters = story_service.get_story_chapters(story_id)

        # 转换为ChapterDetail模型
        chapter_details = []
        for chapter in chapters:
            chapter_detail = ChapterDetail(
                id=str(chapter.id),
                story_id=str(chapter.story_id),
                chapter_number=chapter.chapter_number,
                title=chapter.title,
                content=chapter.content,
                summary=getattr(chapter, 'summary', None),
                created_at=chapter.created_at
            )
            chapter_details.append(chapter_detail)

        return ChaptersListResponse.create(
            story_id=str(story_id),
            chapters=chapter_details
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取章节列表失败: {str(e)}"
        )


class GenerateChapterRequest(BaseModel):
    provider: str = None  # LLM服务商：siliconflow, openrouter, gemini
    user_choice: str = None  # 用户在上一章的选择（第一章时为空）
    choice_type: str = "ai_generated"  # 选择类型：ai_generated 或 user_custom

@router.post("/{story_id}/chapters/generate/stream")
async def generate_chapter_unified_stream(
    story_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
    request: GenerateChapterRequest = GenerateChapterRequest()
):
    """
    🎯 统一的章节生成接口（流式） - 唯一需要的章节生成API

    功能：
    - 自动判断生成第一章还是后续章节
    - 流式返回章节内容
    - 自动保存章节和选择选项
    - 包含完整的错误处理

    判断逻辑：
    - 如果 user_choice 为空且当前章节数为0，生成第一章
    - 如果 user_choice 不为空，生成后续章节

    返回数据：
    - 流式章节内容
    - 章节保存状态
    - 选择选项（章节完成时）
    - 错误信息（如有）

    ⚠️ 注意：这是唯一推荐使用的章节生成接口，其他接口将被标记为废弃
    """
    from fastapi.responses import StreamingResponse
    import json
    import asyncio

    async def generate_stream():
        try:
            story_service = StoryService(db)
            story = story_service.get_story(story_id)

            if not story:
                yield f"data: {json.dumps({'type': 'error', 'message': '故事不存在'})}\n\n"
                return

            # 检查用户权限
            if story.user_id != current_user.id:
                yield f"data: {json.dumps({'type': 'error', 'message': '无权访问此故事'})}\n\n"
                return

            # 准备请求参数
            provider = request.provider if request else None
            user_choice = request.user_choice if request else None
            choice_type = request.choice_type if request else "ai_generated"

            # 判断是第一章还是后续章节
            is_first_chapter = (story.current_chapter_number == 0 and not user_choice)

            if is_first_chapter:
                # 生成第一章
                chapter = None
                saved_choices = []

                async for chunk in story_service.generate_first_chapter_stream(
                    story_id, provider=provider
                ):
                    chunk_type = chunk.get('type')

                    if chunk_type == 'content':
                        # 转发内容流
                        yield f"data: {json.dumps({'type': 'content', 'data': chunk.get('data', '')})}\n\n"
                    elif chunk_type == 'complete':
                        # 章节已在 generate_first_chapter_stream 中保存完成
                        chapter_data = chunk.get('chapter', {})

                        # 获取保存的章节对象
                        if chapter_data.get('id'):
                            chapter = story_service.get_chapter(chapter_data['id'])
                            saved_choices = story_service.get_chapter_choices(chapter.id)

                        logger.info(f"第一章生成完成，章节ID: {chapter.id if chapter else 'None'}")
                        break
                    elif chunk_type == 'error':
                        # 转发错误信息
                        yield f"data: {json.dumps(chunk)}\n\n"
                        return

                # 如果没有获取到章节信息，说明保存失败
                if not chapter:
                    yield f"data: {json.dumps({'type': 'error', 'message': '章节保存失败'})}\n\n"
                    return

            else:
                # 生成后续章节
                if not user_choice:
                    yield f"data: {json.dumps({'type': 'error', 'message': '生成后续章节需要提供用户选择'})}\n\n"
                    return

                # 保存用户选择
                story_service.save_user_choice(
                    story_id=story.id,
                    chapter_number=story.current_chapter_number,
                    choice_text=user_choice,
                    choice_type=choice_type
                )

                # 生成下一章
                chapter = None
                saved_choices = []

                async for chunk in story_service.generate_next_chapter_stream(
                    story_id=story_id,
                    user_choice=user_choice,
                    provider=provider
                ):
                    chunk_type = chunk.get('type')

                    if chunk_type == 'content':
                        # 转发内容流
                        yield f"data: {json.dumps({'type': 'content', 'data': chunk.get('data', '')})}\n\n"
                    elif chunk_type == 'chapter_complete':
                        # 章节已在 generate_next_chapter_stream 中保存完成
                        chapter_data = chunk.get('data', {})

                        # 获取保存的章节对象
                        if chapter_data.get('chapter_id'):
                            chapter = story_service.get_chapter(chapter_data['chapter_id'])
                            if chapter:
                                saved_choices = story_service.get_chapter_choices(chapter.id)

                        logger.info(f"后续章节生成完成，章节ID: {chapter.id if chapter else 'None'}")
                        break
                    elif chunk_type == 'error':
                        # 转发错误信息
                        yield f"data: {json.dumps(chunk)}\n\n"
                        return

                # 如果没有获取到章节信息，说明保存失败
                if not chapter:
                    yield f"data: {json.dumps({'type': 'error', 'message': '章节保存失败'})}\n\n"
                    return

            # 输出完成信息
            complete_data = {
                "type": "complete",
                "data": {
                    "chapter_id": str(chapter.id),
                    "chapter_number": chapter.chapter_number,
                    "title": getattr(chapter, 'title', f"第{chapter.chapter_number}章"),
                    "choices": [
                        {
                            "id": str(choice.id),
                            "text": choice.choice_text,
                            "type": "ai_generated"
                        }
                        for choice in saved_choices
                    ]
                }
            }
            yield f"data: {json.dumps(complete_data)}\n\n"

        except Exception as e:
            logger.error(f"生成章节失败: {e}")
            yield f"data: {json.dumps({'type': 'error', 'message': f'生成章节失败: {str(e)}'})}\n\n"

    return StreamingResponse(
        generate_stream(),
        media_type="text/plain",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "*",
        }
    )




@router.post("/{story_id}/chapters/{chapter_number}/choice")
async def save_user_choice(
    story_id: str,
    chapter_number: int,
    choice_text: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    保存用户在某章节的选择

    用于：
    1. 记录用户的选择历史
    2. 为生成下一章节提供依据
    3. 支持故事分支管理
    """
    try:
        story_service = StoryService(db)
        story = story_service.get_story(story_id, current_user.id)
        if not story:
            raise HTTPException(status_code=404, detail="故事不存在")

        # 保存用户选择
        choice_saved = story_service.save_user_choice(
            story_id=story_id,
            chapter_number=chapter_number,
            choice_text=choice_text
        )

        return {
            "success": True,
            "story_id": story_id,
            "chapter_number": chapter_number,
            "choice_text": choice_text,
            "message": "用户选择已保存"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"保存用户选择失败: {e}")
        raise HTTPException(status_code=500, detail=f"保存用户选择失败: {str(e)}")

@router.get("/{story_id}/choices",
           response_model=StoryChoicesHistoryResponse,
           responses=STANDARD_RESPONSES)
async def get_story_choices_history(
    story_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> StoryChoicesHistoryResponse:
    """获取故事选择历史"""
    try:
        story_service = StoryService(db)
        story = story_service.get_story(story_id)

        if not story:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="故事不存在"
            )

        # 检查用户权限
        if story.user_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问此故事"
            )

        choices_history = story_service.get_story_choices_history(story_id)

        return StoryChoicesHistoryResponse.create(
            story_id=str(story_id),
            choices_history=choices_history
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取选择历史失败: {str(e)}"
        )

@router.delete("/{story_id}", response_model=StoryResponse)
async def delete_story(
    story_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """删除故事及其所有相关数据"""
    try:
        story_service = StoryService(db)
        story = story_service.get_story(story_id)
        
        if not story:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="故事不存在"
            )
        
        # 检查用户权限
        if story.user_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问此故事"
            )
        
        # 删除故事
        story_service.delete_story(story_id)
        
        return StoryResponse(
            success=True,
            data={"story_id": str(story_id)},
            message="故事删除成功"
        )
    except HTTPException:
        raise
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除故事失败: {str(e)}"
        )

# ❌ 已移除：世界观查询接口
#
# 原因：
# 1. 用户不需要直接查看世界观设定
# 2. 世界观信息会在章节内容中自然体现
# 3. 世界观主要用于后端AI生成时的上下文
# 4. 简化API结构，专注于核心功能
# 5. 避免信息过载，提升用户体验
#
# 世界观的作用：
# - 在创建故事时自动生成
# - 作为AI生成章节时的背景上下文
# - 用户通过阅读章节内容自然了解世界设定
# - 保持故事的神秘感和沉浸感

# ❌ 已移除：POST /{story_id}/worldview 世界观生成接口
#
# 原因：
# 1. 世界观已集成到故事创建流程中，无需单独生成
# 2. 避免用户意外覆盖已有世界观
# 3. 简化API结构，减少用户理解成本
# 4. 保持自动化流程的一致性
#
# 替代方案：
# - 世界观在创建故事时自动生成
# - 如需查看世界观，使用 GET /{story_id}/worldview

