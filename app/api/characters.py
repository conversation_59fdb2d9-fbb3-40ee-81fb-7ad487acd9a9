"""
角色档案管理API
提供角色档案的查询、创建、更新等功能
"""

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import Optional, Dict, Any
import uuid

from app.database import get_db
from app.services.character_service import CharacterService
from app.models.character import Character
from app.utils.logger import get_logger

logger = get_logger(__name__)
router = APIRouter(prefix="/api/characters", tags=["角色档案"])

@router.get("/{story_id}/protagonist")
async def get_protagonist(
    story_id: str,
    db: Session = Depends(get_db)
):
    """获取故事的主角信息"""
    try:
        character_service = CharacterService(db)
        protagonist = character_service.get_protagonist(story_id)

        if not protagonist:
            raise HTTPException(status_code=404, detail="主角不存在")

        return {
            "success": True,
            "data": protagonist.to_dict()
        }

    except ValueError:
        raise HTTPException(status_code=400, detail="无效的故事ID格式")
    except Exception as e:
        logger.error(f"获取主角失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取主角失败: {str(e)}")

@router.get("/{story_id}/characters")
async def get_all_characters(
    story_id: str,
    db: Session = Depends(get_db)
):
    """获取故事的所有角色"""
    try:
        character_service = CharacterService(db)
        characters = character_service.get_all_characters(story_id)

        return {
            "success": True,
            "data": [char.to_dict() for char in characters],
            "count": len(characters)
        }

    except ValueError:
        raise HTTPException(status_code=400, detail="无效的故事ID格式")
    except Exception as e:
        logger.error(f"获取角色列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取角色列表失败: {str(e)}")

@router.post("/{story_id}/protagonist/create")
async def create_protagonist(
    story_id: str,
    worldview_data: Dict[str, Any],
    story_style: str,
    db: Session = Depends(get_db)
):
    """为故事创建主角"""
    try:
        character_service = CharacterService(db)
        protagonist = await character_service.create_protagonist(
            story_id=story_id,
            worldview_data=worldview_data,
            story_style=story_style
        )

        return {
            "success": True,
            "data": protagonist.to_dict(),
            "message": f"主角创建成功：{protagonist.name}"
        }

    except ValueError:
        raise HTTPException(status_code=400, detail="无效的故事ID格式")
    except Exception as e:
        logger.error(f"创建主角失败: {e}")
        raise HTTPException(status_code=500, detail=f"创建主角失败: {str(e)}")

@router.put("/character/{character_id}/state")
async def update_character_state(
    character_id: str,
    new_state: str,
    location: Optional[str] = None,
    mood: Optional[str] = None,
    health: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """动态更新角色状态"""
    try:
        character_service = CharacterService(db)
        success = await character_service.update_character_state(
            character_id=character_id,
            new_state=new_state,
            location=location,
            mood=mood,
            health=health
        )

        if not success:
            raise HTTPException(status_code=404, detail="角色不存在或更新失败")

        return {
            "success": True,
            "message": "角色状态更新成功"
        }

    except ValueError:
        raise HTTPException(status_code=400, detail="无效的角色ID格式")
    except Exception as e:
        logger.error(f"更新角色状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"更新角色状态失败: {str(e)}")

@router.put("/character/{character_id}/power")
async def update_character_power(
    character_id: str,
    new_level: int,
    reason: str = "",
    db: Session = Depends(get_db)
):
    """更新角色实力等级"""
    try:
        character_service = CharacterService(db)
        success = await character_service.update_character_power_level(
            character_id=character_id,
            new_level=new_level,
            reason=reason
        )

        if not success:
            raise HTTPException(status_code=404, detail="角色不存在或更新失败")

        return {
            "success": True,
            "message": f"角色实力更新成功，新等级：{new_level}"
        }

    except ValueError:
        raise HTTPException(status_code=400, detail="无效的角色ID或等级")
    except Exception as e:
        logger.error(f"更新角色实力失败: {e}")
        raise HTTPException(status_code=500, detail=f"更新角色实力失败: {str(e)}")

@router.get("/{story_id}/profile/summary")
async def get_character_summary(
    story_id: str,
    db: Session = Depends(get_db)
):
    """获取角色档案的简要信息"""
    try:
        character_service = CharacterService(db)
        character_profile = character_service.get_character_profile(uuid.UUID(story_id))
        
        if not character_profile:
            return {
                "success": True,
                "data": {
                    "has_profile": False,
                    "message": "角色档案不存在"
                }
            }
        
        return {
            "success": True,
            "data": {
                "has_profile": True,
                "name": character_profile.name,
                "personality_traits": character_profile.personality_traits,
                "current_state": character_profile.current_state
            }
        }
        
    except ValueError:
        raise HTTPException(status_code=400, detail="无效的故事ID格式")
    except Exception as e:
        logger.error(f"获取角色摘要失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取角色摘要失败: {str(e)}")
