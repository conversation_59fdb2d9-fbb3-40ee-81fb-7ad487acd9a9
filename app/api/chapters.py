from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.responses import StreamingResponse
from sqlalchemy.orm import Session
from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field
import json

from app.database import get_db
from app.models import ChoiceType, User
from app.models.responses import (
    SuccessResponse, ErrorResponse,
    ChapterResponse as ChapterResponseModel, STANDARD_RESPONSES,
    ChapterDetail, ChapterDetailWithChoices, ChapterWithChoicesResponse,
    ChoicesListResponse, ChoiceDetail
)
from app.services import StoryService
from .auth import get_current_user
from app.utils.logger import get_logger

logger = get_logger(__name__)

router = APIRouter(prefix="/chapters", tags=["章节"])

# Pydantic模型
class SubmitChoiceRequest(BaseModel):
    """提交选择请求模型"""
    choice_id: Optional[str] = Field(None, description="AI生成的选择选项ID")
    custom_choice: Optional[str] = Field(None, description="用户自定义选择内容", max_length=200)

    class Config:
        json_schema_extra = {
            "example": {
                "choice_id": "choice-uuid-123",
                "custom_choice": None
            }
        }

@router.get("/{chapter_id}",
           response_model=ChapterResponseModel,
           responses=STANDARD_RESPONSES)
async def get_chapter(
    chapter_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> ChapterResponseModel:
    """获取章节详情"""
    try:
        story_service = StoryService(db)
        chapter = story_service.get_chapter(chapter_id)

        if not chapter:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="章节不存在"
            )

        # 检查用户权限
        story = story_service.get_story(chapter.story_id)
        if story and story.user_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问此章节"
            )

        # 转换为ChapterDetail模型
        chapter_detail = ChapterDetail(
            id=str(chapter.id),
            story_id=str(chapter.story_id),
            chapter_number=chapter.chapter_number,
            title=chapter.title,
            content=chapter.content,
            summary=getattr(chapter, 'summary', None),
            created_at=chapter.created_at
        )

        return ChapterResponseModel(
            data=chapter_detail
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取章节失败: {str(e)}"
        )


@router.get("/{chapter_id}/detail",
           response_model=ChapterWithChoicesResponse,
           responses=STANDARD_RESPONSES)
async def get_chapter_with_choices(
    chapter_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> ChapterWithChoicesResponse:
    """获取章节详情（包含选择选项）"""
    try:
        story_service = StoryService(db)
        chapter = story_service.get_chapter(chapter_id)

        if not chapter:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="章节不存在"
            )

        # 检查用户权限
        story = story_service.get_story(chapter.story_id)
        if story and story.user_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问此章节"
            )

        # 获取选择选项
        choices = story_service.get_chapter_choices(chapter_id)
        choice_details = [
            ChoiceDetail(
                id=str(choice.id),
                choice_text=choice.choice_text,
                choice_type=choice.choice_type.value
            )
            for choice in choices
        ]

        # 转换为ChapterDetailWithChoices模型
        chapter_detail = ChapterDetailWithChoices(
            id=str(chapter.id),
            story_id=str(chapter.story_id),
            chapter_number=chapter.chapter_number,
            title=chapter.title,
            content=chapter.content,
            summary=getattr(chapter, 'summary', None),
            created_at=chapter.created_at,
            choices=choice_details
        )

        return ChapterWithChoicesResponse(
            data=chapter_detail
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取章节详情失败: {str(e)}"
        )


@router.get("/{chapter_id}/choices",
           response_model=ChoicesListResponse,
           responses=STANDARD_RESPONSES)
async def get_chapter_choices(
    chapter_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> ChoicesListResponse:
    """获取章节的选择选项"""
    try:
        story_service = StoryService(db)
        chapter = story_service.get_chapter(chapter_id)

        if not chapter:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="章节不存在"
            )

        # 检查用户权限
        story = story_service.get_story(chapter.story_id)
        if story and story.user_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问此章节"
            )

        choices = story_service.get_chapter_choices(chapter_id)

        # 转换为ChoiceDetail模型
        choice_details = []
        for choice in choices:
            choice_detail = ChoiceDetail(
                id=str(choice.id),
                chapter_id=str(choice.chapter_id),
                choice_text=choice.choice_text,
                choice_number=getattr(choice, 'choice_number', 0),
                is_selected=getattr(choice, 'is_selected', False),
                created_at=choice.created_at
            )
            choice_details.append(choice_detail)

        return ChoicesListResponse.create(
            chapter_id=str(chapter_id),
            choices=choice_details
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取选择选项失败: {str(e)}"
        )


@router.post("/{chapter_id}/choices",
            responses=STANDARD_RESPONSES)
async def submit_choice_and_generate_next_chapter(
    chapter_id: str,
    request: SubmitChoiceRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """提交选择并生成下一章节"""
    try:
        story_service = StoryService(db)
        chapter = story_service.get_chapter(chapter_id)

        if not chapter:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="章节不存在"
            )

        # 检查用户权限
        story = story_service.get_story(chapter.story_id)
        if story and story.user_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问此章节"
            )

        # 验证请求参数
        if not request.choice_id and not request.custom_choice:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="必须提供choice_id或custom_choice之一"
            )

        if request.choice_id and request.custom_choice:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="choice_id和custom_choice不能同时提供"
            )

        # 确定用户选择
        if request.choice_id:
            # 验证choice_id是否存在
            choices = story_service.get_chapter_choices(chapter_id)
            selected_choice = None
            for choice in choices:
                if str(choice.id) == request.choice_id:
                    selected_choice = choice
                    break

            if not selected_choice:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="选择选项不存在"
                )

            user_choice = selected_choice.choice_text
        else:
            user_choice = request.custom_choice

        logger.info(f"用户选择: {user_choice}")

        # 保存用户选择
        story_service.save_user_choice(
            story_id=story.id,
            chapter_number=chapter.chapter_number,
            choice_text=user_choice,
            choice_type="ai_generated" if request.choice_id else "user_custom"
        )

        # 流式生成下一章节
        async def generate_stream():
            try:
                async for chunk in story_service.generate_next_chapter_stream(
                    story_id=story.id,
                    user_choice=user_choice,
                    provider="gemini"  # 可以从配置中获取
                ):
                    # 将chunk转换为SSE格式
                    yield f"data: {json.dumps(chunk, ensure_ascii=False)}\n\n"

                # 发送结束信号
                yield f"data: {json.dumps({'type': 'stream_end'}, ensure_ascii=False)}\n\n"

            except Exception as e:
                logger.error(f"流式生成失败: {e}")
                error_chunk = {
                    "type": "error",
                    "message": f"生成下一章节失败: {str(e)}"
                }
                yield f"data: {json.dumps(error_chunk, ensure_ascii=False)}\n\n"

        return StreamingResponse(
            generate_stream(),
            media_type="text/plain",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Content-Type": "text/plain; charset=utf-8"
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"提交选择失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"提交选择失败: {str(e)}"
        )

