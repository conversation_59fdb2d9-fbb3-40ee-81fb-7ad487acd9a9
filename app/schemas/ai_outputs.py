"""
AI输出结构化数据模式
定义AI生成内容的标准格式，支持原生结构化输出

版本管理：
- v1.0: 初始版本，支持角色档案和章节选项
- 后续版本在此基础上扩展，保持向后兼容
"""

from typing import List, Optional
from pydantic import BaseModel, Field, validator
from enum import Enum


class AIOutputVersion(str, Enum):
    """AI输出模式版本"""
    V1_0 = "1.0"


class CharacterProfileOutput(BaseModel):
    """
    角色档案输出模式 v1.0
    用于AI生成角色档案的结构化输出
    """
    
    # 版本信息
    schema_version: str = Field(default=AIOutputVersion.V1_0, description="模式版本")
    
    # 核心角色信息
    name: str = Field(..., min_length=2, max_length=10, description="角色的具体中文姓名，不能使用'主角'、'我'等代称")
    personality_traits: str = Field(..., min_length=10, max_length=200, description="3-5个核心性格特征，用逗号分隔")
    speaking_style: str = Field(..., min_length=10, max_length=150, description="角色的语言风格和表达习惯")
    background: str = Field(..., min_length=20, max_length=300, description="角色的出身、经历、当前处境")
    appearance: str = Field(..., min_length=10, max_length=200, description="简洁的外貌描述，突出特色")
    core_abilities: str = Field(..., min_length=10, max_length=200, description="角色的主要能力或天赋")
    growth_goals: str = Field(..., min_length=10, max_length=200, description="角色想要达成的目标")
    key_relationships: str = Field(..., min_length=10, max_length=200, description="与其他重要角色的关系")
    current_state: str = Field(default="故事开始", max_length=100, description="角色当前状态")

    @validator('name')
    def validate_name(cls, v):
        """验证角色姓名"""
        forbidden_names = ['主角', '我', '他', '她', '角色', '人物']
        if v in forbidden_names:
            raise ValueError(f"角色姓名不能使用: {v}")
        return v

    @validator('personality_traits')
    def validate_personality_traits(cls, v):
        """验证性格特点"""
        # 支持多种分隔符：逗号、顿号、分号
        import re
        traits = re.split(r'[,，、;；]', v)
        traits = [trait.strip() for trait in traits if trait.strip()]
        if len(traits) < 3:
            raise ValueError(f"性格特点至少需要3个，当前只有{len(traits)}个: {traits}")
        return v

    class Config:
        json_schema_extra = {
            "example": {
                "schema_version": "1.0",
                "name": "林浩",
                "personality_traits": "倔强不屈，正义感强，容易冲动，聪明机敏，重情重义",
                "speaking_style": "简洁直接，不喜废话，语气坚定，偶尔带有少年意气",
                "background": "原太虚宗外门弟子，因天赋异禀遭同门嫉妒，被诬陷偷盗宗门秘籍而逐出师门",
                "appearance": "剑眉星目，身材修长，眼神坚毅，左手腕有一道神秘印记",
                "core_abilities": "拥有罕见的星辰血脉，能够感知和操控星辰之力",
                "growth_goals": "证明自己的清白，重回宗门，最终成为守护苍生的强者",
                "key_relationships": "与师父关系复杂（既有师恩又有被逐之恨），与师兄弟关系微妙",
                "current_state": "刚被逐出师门，心情复杂，决心证明自己"
            }
        }


class ChapterChoicesOutput(BaseModel):
    """
    章节选项输出模式 v1.0
    用于AI生成章节选择选项的结构化输出
    """
    
    # 版本信息
    schema_version: str = Field(default=AIOutputVersion.V1_0, description="模式版本")
    
    # 选项内容
    choices: List[str] = Field(..., min_items=3, max_items=3, description="3个选择选项，每个选项简洁明了")

    @validator('choices')
    def validate_choices(cls, v):
        """验证选项内容"""
        if len(v) != 3:
            raise ValueError("必须提供恰好3个选择选项")
        
        for i, choice in enumerate(v):
            if len(choice) < 5 or len(choice) > 30:
                raise ValueError(f"选项{i+1}长度应在5-30字之间")
            
            if choice.strip() != choice:
                v[i] = choice.strip()
        
        return v

    class Config:
        json_schema_extra = {
            "example": {
                "schema_version": "1.0",
                "choices": [
                    "立即冲向敌人，正面对抗",
                    "暗中观察，寻找机会",
                    "尝试绕路避开危险"
                ]
            }
        }


class ChapterOutput(BaseModel):
    """
    章节内容输出模式 v1.0
    用于AI生成章节内容的结构化输出
    """
    
    # 版本信息
    schema_version: str = Field(default=AIOutputVersion.V1_0, description="模式版本")
    
    # 章节内容
    title: str = Field(..., min_length=2, max_length=15, description="章节标题，简洁有力，不超过15个字")
    content: str = Field(..., min_length=2000, max_length=5000, description="章节正文内容，必须在2000-5000字之间")

    @validator('title')
    def validate_title(cls, v):
        """验证章节标题"""
        if v.startswith('第') and '章' in v:
            raise ValueError("标题不应包含'第X章'格式")
        return v.strip()

    @validator('content')
    def validate_content(cls, v):
        """验证章节内容"""
        content_length = len(v.strip())
        if content_length < 2000:
            raise ValueError(f"章节内容过短，当前{content_length}字，至少需要2000字")
        if content_length > 5000:
            raise ValueError(f"章节内容过长，当前{content_length}字，最多5000字")
        return v.strip()

    class Config:
        json_schema_extra = {
            "example": {
                "schema_version": "1.0",
                "title": "血祭危机",
                "content": "夜色如墨，林浩站在悬崖边缘..."
            }
        }


class ChapterSummaryOutput(BaseModel):
    """
    章节摘要输出模式 v1.0
    用于AI生成章节摘要的结构化输出
    """
    
    # 版本信息
    schema_version: str = Field(default=AIOutputVersion.V1_0, description="模式版本")
    
    # 摘要内容
    summary: str = Field(..., min_length=200, max_length=400, description="章节摘要，200-400字，包含主角状态和故事发展")

    @validator('summary')
    def validate_summary(cls, v):
        """验证摘要内容"""
        summary_length = len(v.strip())
        if summary_length < 200:
            raise ValueError(f"摘要过短，当前{summary_length}字，至少需要200字")
        if summary_length > 400:
            raise ValueError(f"摘要过长，当前{summary_length}字，最多400字")
        return v.strip()

    class Config:
        json_schema_extra = {
            "example": {
                "schema_version": "1.0",
                "summary": "在青铜殿内，林浩正站在青铜殿地面裂开的虚空漩涡边缘，左臂被血契印记化作的金龙咬破，伤口处燃烧着星辰纯阳火，周身九道金光流转。他因觉醒血脉力量而气血翻涌，但精神状态从最初的震惊逐渐转为坚定，意识到自己与上古仙帝的渊源。林浩现拥有星辰纯阳火、星图镜、通体透明的剑以及融合后的九霄天阶法则印记，与三名长老从对峙转为合作关系。当前青铜殿内星光璀璨，虚空漩涡深不见底，林浩正准备做出关键选择。"
            }
        }


class WorldViewOutput(BaseModel):
    """
    世界观输出模式 v1.0
    用于AI生成世界观设定的结构化输出
    """
    
    # 版本信息
    schema_version: str = Field(default=AIOutputVersion.V1_0, description="模式版本")
    
    # 世界观内容
    world_setting: str = Field(..., min_length=50, max_length=500, description="世界背景设定")
    power_system: str = Field(..., min_length=50, max_length=500, description="力量体系说明")
    main_character: str = Field(..., min_length=30, max_length=300, description="主角基本设定")
    story_themes: str = Field(..., min_length=20, max_length=200, description="故事主题")
    key_locations: str = Field(..., min_length=30, max_length=300, description="关键地点")
    important_factions: str = Field(..., min_length=30, max_length=300, description="重要势力")

    class Config:
        json_schema_extra = {
            "example": {
                "schema_version": "1.0",
                "world_setting": "修仙世界，灵气复苏，宗门林立",
                "power_system": "炼气、筑基、金丹、元婴、化神等修炼境界",
                "main_character": "天赋异禀的年轻修士，拥有特殊血脉",
                "story_themes": "成长、复仇、正义、友情",
                "key_locations": "太虚宗、九霄圣庭、无相秘境",
                "important_factions": "太虚宗、魔道联盟、散修联盟"
            }
        }


class CompleteChapterOutput(BaseModel):
    """
    完整章节输出模式 v1.0
    用于AI一次性生成章节内容和选项的结构化输出
    """
    
    # 版本信息
    schema_version: str = Field(default=AIOutputVersion.V1_0, description="模式版本")
    
    # 完整章节内容
    title: str = Field(..., min_length=2, max_length=15, description="章节标题，简洁有力，不超过15个字")
    content: str = Field(..., min_length=2000, max_length=5000, description="章节正文内容，必须在2000-5000字之间")
    choices: List[str] = Field(..., min_items=3, max_items=3, description="3个选择选项")

    @validator('title')
    def validate_title(cls, v):
        """验证章节标题"""
        if v.startswith('第') and '章' in v:
            raise ValueError("标题不应包含'第X章'格式")
        return v.strip()

    @validator('content')
    def validate_content(cls, v):
        """验证章节内容"""
        content_length = len(v.strip())
        if content_length < 2000:
            raise ValueError(f"章节内容过短，当前{content_length}字，至少需要2000字")
        if content_length > 5000:
            raise ValueError(f"章节内容过长，当前{content_length}字，最多5000字")
        return v.strip()

    @validator('choices')
    def validate_choices(cls, v):
        """验证选项内容"""
        if len(v) != 3:
            raise ValueError("必须提供恰好3个选择选项")
        
        for i, choice in enumerate(v):
            if len(choice) < 5 or len(choice) > 30:
                raise ValueError(f"选项{i+1}长度应在5-30字之间")
            
            if choice.strip() != choice:
                v[i] = choice.strip()
        
        return v

    class Config:
        json_schema_extra = {
            "example": {
                "schema_version": "1.0",
                "title": "血祭危机",
                "content": "夜色如墨，林浩站在悬崖边缘...",
                "choices": [
                    "立即冲向敌人，正面对抗",
                    "暗中观察，寻找机会",
                    "尝试绕路避开危险"
                ]
            }
        }
