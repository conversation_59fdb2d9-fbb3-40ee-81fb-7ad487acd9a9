"""
角色数据模型
独立的角色数据表，支持动态更新和扩展
"""

from sqlalchemy import Column, String, Text, DateTime, ForeignKey, <PERSON><PERSON><PERSON>, Integer
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid

from app.database import Base

class Character(Base):
    """角色数据表 - 独立存储角色信息"""
    __tablename__ = "characters"

    # 基础字段 - 使用String类型以匹配现有的Story表
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    story_id = Column(String(36), ForeignKey("stories.id"), nullable=False)
    
    # 角色基本信息
    name = Column(String(100), nullable=False, comment="角色姓名")
    role_type = Column(String(20), default="protagonist", comment="角色类型：protagonist(主角)/supporting(配角)/antagonist(反派)")
    is_active = Column(Boolean, default=True, comment="是否为活跃角色")
    
    # 角色档案信息
    personality_traits = Column(Text, comment="性格特点")
    speaking_style = Column(Text, comment="说话方式")
    background = Column(Text, comment="背景设定")
    appearance = Column(Text, comment="外貌特征")
    core_abilities = Column(Text, comment="核心能力")
    growth_goals = Column(Text, comment="成长目标")
    key_relationships = Column(Text, comment="关键关系")
    
    # 动态状态信息
    current_state = Column(Text, comment="当前状态")
    current_location = Column(String(200), comment="当前位置")
    current_mood = Column(String(100), comment="当前情绪")
    health_status = Column(String(100), comment="健康状态")
    power_level = Column(Integer, default=1, comment="实力等级")
    
    # 故事进展相关
    last_appearance_chapter = Column(Integer, comment="最后出现章节")
    character_arc_stage = Column(String(50), comment="角色弧光阶段")
    development_notes = Column(Text, comment="发展备注")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # 关系
    story = relationship("Story", back_populates="characters")
    
    def __repr__(self):
        return f"<Character(id={self.id}, name='{self.name}', story_id={self.story_id})>"
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            "id": str(self.id),
            "story_id": str(self.story_id),
            "name": self.name,
            "role_type": self.role_type,
            "is_active": self.is_active,
            "personality_traits": self.personality_traits,
            "speaking_style": self.speaking_style,
            "background": self.background,
            "appearance": self.appearance,
            "core_abilities": self.core_abilities,
            "growth_goals": self.growth_goals,
            "key_relationships": self.key_relationships,
            "current_state": self.current_state,
            "current_location": self.current_location,
            "current_mood": self.current_mood,
            "health_status": self.health_status,
            "power_level": self.power_level,
            "last_appearance_chapter": self.last_appearance_chapter,
            "character_arc_stage": self.character_arc_stage,
            "development_notes": self.development_notes,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }
    
    def get_profile_summary(self):
        """获取角色档案摘要"""
        return f"""
角色姓名：{self.name}
角色类型：{self.role_type}
性格特点：{self.personality_traits}
说话方式：{self.speaking_style}
背景设定：{self.background}
当前状态：{self.current_state}
当前位置：{self.current_location or '未知'}
实力等级：{self.power_level}
"""
    
    def get_dynamic_context(self):
        """获取动态上下文信息（用于章节生成）"""
        return f"""
{self.name}当前状态：
- 位置：{self.current_location or '未知'}
- 情绪：{self.current_mood or '平静'}
- 健康：{self.health_status or '良好'}
- 实力等级：{self.power_level}
- 角色发展阶段：{self.character_arc_stage or '初期'}
"""

class CharacterRelationship(Base):
    """角色关系表 - 存储角色间的关系"""
    __tablename__ = "character_relationships"

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    story_id = Column(String(36), ForeignKey("stories.id"), nullable=False)
    character_a_id = Column(String(36), ForeignKey("characters.id"), nullable=False)
    character_b_id = Column(String(36), ForeignKey("characters.id"), nullable=False)
    
    relationship_type = Column(String(50), comment="关系类型：friend/enemy/mentor/rival/family等")
    relationship_description = Column(Text, comment="关系描述")
    relationship_strength = Column(Integer, default=5, comment="关系强度(1-10)")
    is_mutual = Column(Boolean, default=True, comment="是否为双向关系")
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # 关系
    character_a = relationship("Character", foreign_keys=[character_a_id])
    character_b = relationship("Character", foreign_keys=[character_b_id])
    story = relationship("Story")

class CharacterEvent(Base):
    """角色事件表 - 记录角色的重要事件和发展"""
    __tablename__ = "character_events"

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    character_id = Column(String(36), ForeignKey("characters.id"), nullable=False)
    chapter_id = Column(String(36), ForeignKey("chapters.id"), nullable=True)
    
    event_type = Column(String(50), comment="事件类型：growth/conflict/discovery/relationship等")
    event_description = Column(Text, comment="事件描述")
    impact_level = Column(Integer, default=3, comment="影响程度(1-5)")
    
    # 状态变化记录
    state_before = Column(Text, comment="事件前状态")
    state_after = Column(Text, comment="事件后状态")
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # 关系
    character = relationship("Character")
    chapter = relationship("Chapter")
