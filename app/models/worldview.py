from sqlalchemy import Column, String, DateTime, Text, ForeignKey, JSO<PERSON>
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from datetime import datetime
import uuid
from ..database.connection import Base

class WorldView(Base):
    """世界观框架模型"""
    __tablename__ = "worldviews"

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()), index=True)
    story_id = Column(String(36), ForeignKey("stories.id"), nullable=False, unique=True, index=True)

    # 简化为单一JSON字段存储所有世界观数据
    worldview_data = Column(JSON, nullable=False, default=dict)  # 完整的世界观数据

    # 保留基本描述字段用于快速查询和显示
    summary = Column(Text)  # 世界观简要描述
    
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # 关联关系
    story = relationship("Story", back_populates="worldview")
    
    def __repr__(self):
        return f"<WorldView(id={self.id}, story_id={self.story_id})>"
    
    def to_dict(self):
        return {
            "id": str(self.id),
            "story_id": str(self.story_id),
            "worldview_data": self.worldview_data,
            "summary": self.summary,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat()
        }

    def get_context_summary(self) -> str:
        """获取世界观的上下文摘要，用于章节生成"""
        if not self.worldview_data:
            return self.summary or "未知世界"

        # 构建简化的上下文摘要
        context_parts = []

        # 世界名称
        if self.worldview_data.get('world_name'):
            context_parts.append(f"世界: {self.worldview_data['world_name']}")

        # 力量体系（简化版）
        if self.worldview_data.get('power_system'):
            power_system = self.worldview_data['power_system']
            if isinstance(power_system, str):
                context_parts.append(f"力量体系: {power_system}")
            else:
                context_parts.append(f"力量体系: {str(power_system)}")

        # 地理环境（简化版）
        if self.worldview_data.get('geography'):
            geography = self.worldview_data['geography']
            if isinstance(geography, str):
                context_parts.append(f"地理环境: {geography}")
            else:
                context_parts.append(f"地理环境: {str(geography)}")

        # 主角信息（简化版）
        if self.worldview_data.get('main_character'):
            main_char = self.worldview_data['main_character']
            if main_char:
                context_parts.append(f"主角设定: {str(main_char)}")

        # 兼容旧格式的字段
        if self.worldview_data.get('world_setting'):
            context_parts.append(f"世界设定: {self.worldview_data['world_setting'][:200]}")

        return "\n\n".join(context_parts) if context_parts else self.summary or "未知世界"