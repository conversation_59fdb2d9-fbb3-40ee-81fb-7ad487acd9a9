from sqlalchemy.orm import Session
from typing import List, Dict, Any, Optional
from app.models import Story, Chapter, Choice, StoryStyle, StoryStatus, ChoiceType, WorldView
from app.services.ai_service import ai_service, AIService
from app.services.worldview_service import WorldViewService
from app.utils.logger import get_logger
from app.prompts.unified_manager import unified_prompt_manager
import uuid

logger = get_logger(__name__)

class StoryService:
    def __init__(self, db: Session):
        self.db = db
        self.worldview_service = WorldViewService(db)
    
    def create_story(self, style: StoryStyle, title: str = None, user_id: str = None) -> Story:
        """创建新故事"""
        if not title:
            style_titles = {
                StoryStyle.XIANXIA: "修仙传奇",
                StoryStyle.WUXIA: "江湖风云",
                StoryStyle.SCIFI: "星际探索"
            }
            title = style_titles.get(style, "未知冒险")
        
        story = Story(
            title=title,
            style=style,
            status=StoryStatus.ACTIVE,
            current_chapter_number=0,
            user_id=user_id
        )
        
        self.db.add(story)
        self.db.commit()
        self.db.refresh(story)
        
        return story
    
    async def create_story_with_worldview(self, style: StoryStyle, title: str = None, user_id: str = None, story_theme: str = None, provider: str = None) -> Dict[str, Any]:
        """创建新故事并生成世界观框架（兼容旧接口）"""
        try:
            # 创建故事
            story = self.create_story(style, title, user_id)

            # 生成世界观框架
            worldview = await self.worldview_service.create_worldview(
                story_id=story.id,
                story_theme=story_theme,
                provider=provider
            )

            return {
                "story": story.to_dict(),
                "worldview": worldview.to_dict()
            }

        except Exception as e:
            # 如果世界观生成失败，删除已创建的故事
            if 'story' in locals():
                self.delete_story(story.id)
            raise e

    async def create_story_with_ai_generation(self, style: StoryStyle, title: str = None, user_id: str = None, provider: str = None) -> Dict[str, Any]:
        """智能创建故事：AI自动生成标题和世界观"""
        try:
            # 如果没有提供标题，让AI生成一个
            if not title:
                title = await self._generate_story_title(style, provider)
                logger.info(f"AI生成的故事标题: {title}")

            # 创建故事
            story = self.create_story(style, title, user_id)

            # AI自动生成世界观（不需要用户提供主题）
            worldview = await self.worldview_service.create_worldview_auto(
                story_id=story.id,
                story_title=title,
                story_style=style,
                provider=provider
            )

            return {
                "story": story.to_dict(),
                "worldview": worldview.to_dict()
            }

        except Exception as e:
            # 如果生成失败，删除已创建的故事
            if 'story' in locals():
                self.delete_story(story.id)
            raise e

    async def _generate_story_title(self, style: StoryStyle, provider: str = None) -> str:
        """根据风格生成故事标题"""
        try:
            # 使用统一提示词管理器生成标题提示词
            prompt = unified_prompt_manager.get_title_prompt(style)

            # 使用AI服务生成标题
            from app.services.ai_service import ai_service
            title = await ai_service._get_provider(provider).generate_text(prompt)

            # 清理标题
            title = title.strip().replace('"', '').replace('《', '').replace('》', '')

            # 如果标题太长，截取前6个字
            if len(title) > 6:
                title = title[:6]

            # 如果生成失败，使用默认标题
            if not title or len(title) < 2:
                default_titles = {
                    StoryStyle.XIANXIA: "修仙传奇",
                    StoryStyle.WUXIA: "江湖风云",
                    StoryStyle.SCIFI: "星际探索"
                }
                title = default_titles.get(style, "未知冒险")

            return title

        except Exception as e:
            logger.error(f"生成故事标题失败: {e}")
            # 使用默认标题
            default_titles = {
                StoryStyle.XIANXIA: "修仙传奇",
                StoryStyle.WUXIA: "江湖风云",
                StoryStyle.SCIFI: "星际探索"
            }
            return default_titles.get(style, "未知冒险")
    
    def get_story(self, story_id: uuid.UUID) -> Optional[Story]:
        """获取故事详情"""
        # 将UUID转换为字符串，因为数据库中存储的是字符串
        story_id_str = str(story_id) if isinstance(story_id, uuid.UUID) else story_id
        return self.db.query(Story).filter(Story.id == story_id_str).first()
    
    def get_all_stories(self) -> List[Story]:
        """获取所有故事列表"""
        return self.db.query(Story).order_by(Story.created_at.desc()).all()
    
    def get_user_stories(self, user_id: str) -> List[Story]:
        """获取指定用户的故事列表"""
        return self.db.query(Story).filter(
            Story.user_id == user_id
        ).order_by(Story.created_at.desc()).all()
    
    def get_story_chapters(self, story_id: uuid.UUID) -> List[Chapter]:
        """获取故事的所有章节"""
        story_id_str = str(story_id) if isinstance(story_id, uuid.UUID) else story_id
        return self.db.query(Chapter).filter(
            Chapter.story_id == story_id_str
        ).order_by(Chapter.chapter_number).all()
    
    def get_story_choices_history(self, story_id: uuid.UUID) -> List[Dict[str, Any]]:
        """获取故事的选择历史"""
        chapters = self.get_story_chapters(story_id)
        choices_history = []
        
        for chapter in chapters:
            selected_choice = self.db.query(Choice).filter(
                Choice.chapter_id == chapter.id,
                Choice.is_selected == True
            ).first()
            
            if selected_choice:
                choices_history.append({
                    "chapter_number": chapter.chapter_number,
                    "chapter_title": chapter.title,
                    "choice": selected_choice.to_dict()
                })
        
        return choices_history

    async def generate_chapter_summary(self, chapter_content: str, provider: str = None) -> str:
        """生成章节摘要"""
        return await ai_service.generate_chapter_summary(chapter_content, provider)

    async def generate_chapter_choices(self, chapter_content: str, story_style: StoryStyle = None, provider: str = None) -> List[str]:
        """生成章节选项"""
        if not story_style:
            story_style = StoryStyle.XIANXIA  # 默认风格
        return await ai_service.generate_choices(chapter_content, story_style, provider)

    def save_chapter_with_summary(self, story_id: uuid.UUID, content: str, summary: str, chapter_number: int) -> Chapter:
        """保存章节和摘要"""
        chapter = Chapter(
            story_id=story_id,
            chapter_number=chapter_number,
            title=f"第{chapter_number}章",
            content=content,
            summary=summary
        )

        self.db.add(chapter)

        # 更新故事的当前章节数
        story = self.get_story(story_id)
        if story:
            story.current_chapter_number = chapter_number

        self.db.commit()
        self.db.refresh(chapter)

        return chapter

    def save_chapter_choices(self, chapter_id: uuid.UUID, choices: List[str]) -> List[Choice]:
        """保存章节选项"""
        saved_choices = []

        for i, choice_text in enumerate(choices):
            choice = Choice(
                chapter_id=chapter_id,
                choice_text=choice_text,
                choice_type=ChoiceType.AI_GENERATED,
                is_selected=False
            )
            self.db.add(choice)
            saved_choices.append(choice)

        self.db.commit()

        # 刷新所有选项以获取ID
        for choice in saved_choices:
            self.db.refresh(choice)

        return saved_choices

    def get_chapter_summaries_dict(self, story_id: uuid.UUID) -> Dict[str, str]:
        """获取章节摘要字典格式"""
        chapters = self.db.query(Chapter).filter(
            Chapter.story_id == story_id
        ).order_by(Chapter.chapter_number).all()

        summaries = {}
        for chapter in chapters:
            chapter_key = f"第{chapter.chapter_number}章"
            summaries[chapter_key] = chapter.summary or "无摘要"

        return summaries

    async def generate_first_chapter_stream(self, story_id: uuid.UUID, provider: str = None, user_choice: str = None):
        """流式生成故事的第一章"""
        try:
            story = self.get_story(story_id)
            if not story:
                yield {"type": "error", "message": "故事不存在"}
                return

            # 获取世界观框架
            worldview = self.worldview_service.get_worldview(story_id)
            if not worldview:
                yield {"type": "error", "message": "故事缺少世界观框架，请先生成世界观"}
                return

            # 构建故事数据
            story_data = {
                "style": story.style.value,
                "title": story.title,
                "current_chapter_number": 1,
                "chapter_summaries": story.chapter_summaries or [],
                "character_info": story.character_info or {}
            }

            # 获取世界观上下文
            worldview_context = worldview.get_context_summary()

            # 流式生成章节内容
            accumulated_content = ""
            chapter_title = ""
            chapter_saved = False

            async for chunk in ai_service.generate_chapter_stream(
                story_data,
                worldview_context=worldview_context,
                provider=provider
            ):
                    # 兼容处理：chunk可能是StreamChunk对象或字典
                    if hasattr(chunk, 'type'):
                        # 新格式：StreamChunk对象
                        chunk_type = chunk.type
                        chunk_content = chunk.content
                        chunk_metadata = chunk.metadata or {}
                    else:
                        # 旧格式：字典
                        chunk_type = chunk.get("type", "unknown")
                        chunk_content = chunk.get("content", "")
                        chunk_metadata = chunk.get("data", {})

                    logger.info(f"收到chunk类型: {chunk_type}")

                    if chunk_type == "title":
                        chapter_title = chunk_content
                        logger.info(f"收到标题: {chapter_title}")
                        yield {"type": chunk_type, "content": chunk_content}
                    elif chunk_type == "content":
                        accumulated_content += chunk_content
                        yield {"type": chunk_type, "content": chunk_content}
                    elif chunk_type == "content_complete":
                        # 内容生成完成，但还在等待选择选项
                        logger.info("内容生成完成，等待选择选项")
                        yield {"type": chunk_type, "content": chunk_content}
                    elif chunk_type == "error":
                        # 处理错误信号
                        error_message = chunk_content or "未知错误"
                        logger.error(f"AI服务返回错误: {error_message}")
                        yield {"type": chunk_type, "message": error_message}
                        # 错误后不继续处理
                        break
                    elif chunk_type == "chapter_complete":
                        # 章节完全生成完成（包含选择选项）
                        logger.info("收到章节完成信号，开始保存到数据库")
                        # 从metadata或data中获取数据
                        chapter_data = chunk_metadata

                        # 生成真正的章节摘要
                        try:
                            summary = await self.generate_chapter_summary(chapter_data["content"])
                            logger.info(f"第一章摘要生成成功: {summary[:50]}...")
                        except Exception as e:
                            logger.error(f"生成第一章摘要失败: {e}，使用默认摘要")
                            summary = self._create_fallback_summary(chapter_data["content"], chapter_data["title"])

                        # 创建章节记录
                        chapter = Chapter(
                            story_id=story.id,
                            chapter_number=1,
                            title=chapter_data["title"],
                            content=chapter_data["content"],
                            summary=summary
                        )

                        self.db.add(chapter)
                        self.db.commit()  # 先提交章节以获取ID
                        self.db.refresh(chapter)

                        # 保存选择选项
                        try:
                            choices_text = chapter_data.get("choices", [])

                            for choice_text in choices_text:
                                choice = Choice(
                                    chapter_id=chapter.id,
                                    choice_text=choice_text,
                                    choice_type=ChoiceType.AI_GENERATED
                                )
                                self.db.add(choice)

                            # 更新故事状态
                            story.current_chapter_number = 1
                            story.chapter_summaries = [chapter.summary]

                            self.db.commit()
                            self.db.refresh(chapter)

                            chapter_saved = True
                            logger.info(f"章节 1 保存成功: {chapter_data['title']}")

                            # 发送完成信号，包含章节信息和选择选项
                            choices = self.get_chapter_choices(chapter.id)
                            yield {
                                "type": "complete",
                                "chapter": chapter.to_dict(),
                                "choices": [choice.to_dict() for choice in choices]
                            }

                        except Exception as e:
                            logger.error(f"保存章节数据失败: {e}")
                            yield {"type": "error", "message": f"保存章节数据失败: {str(e)}"}
                    elif chunk_type == "error":
                        error_message = chunk_content or "Unknown error"
                        logger.error(f"AI生成错误: {error_message}")
                        yield {"type": chunk_type, "message": error_message}
                    elif chunk_type == "complete":
                        # 处理完成信号，保存章节数据
                        logger.info("收到完成信号，开始保存到数据库")
                        chapter_data = chunk_metadata

                        # 生成真正的章节摘要
                        try:
                            summary = await self.generate_chapter_summary(chapter_data.get("content", ""))
                            logger.info(f"第一章摘要生成成功: {summary[:50]}...")
                        except Exception as e:
                            logger.error(f"生成第一章摘要失败: {e}，使用默认摘要")
                            summary = self._create_fallback_summary(
                                chapter_data.get("content", ""),
                                chapter_data.get("title", "第一章")
                            )

                        # 保存章节到数据库
                        try:
                            chapter = await self.save_chapter(
                                story_id=story_id,
                                chapter_number=1,
                                title=chapter_data.get("title", "第一章"),
                                content=chapter_data.get("content", ""),
                                choices=chapter_data.get("choices", [])
                            )
                            chapter_saved = True
                            logger.info(f"第一章保存成功，章节ID: {chapter.id}")

                            # 更新故事的当前章节和章节摘要
                            story.current_chapter_number = 1
                            story.chapter_summaries = [summary]
                            self.db.commit()
                            logger.info("故事状态更新成功")

                            # 发送完成信号，包含章节ID
                            yield {
                                "type": "chapter_complete",
                                "data": {
                                    "chapter_id": str(chapter.id),
                                    "title": chapter_data.get("title", "第一章"),
                                    "content": chapter_data.get("content", ""),
                                    "choices": chapter_data.get("choices", []),
                                    "summary": summary
                                }
                            }

                        except Exception as e:
                            logger.error(f"保存章节数据失败: {e}")
                            yield {"type": "error", "message": f"保存章节数据失败: {str(e)}"}
                    else:
                        logger.warning(f"未知chunk类型: {chunk_type}")
                        yield {"type": chunk_type, "content": chunk_content}

            # 流程结束后检查是否成功保存
            if not chapter_saved and accumulated_content and len(accumulated_content.strip()) > 500:
                    logger.warning("章节生成流程异常结束，尝试恢复保存累积的内容")

                    try:
                        # 使用累积的数据保存章节
                        title = chapter_title or f"第1章"
                        content = accumulated_content.strip()

                        # 确保内容以合适的标点结尾
                        if not content.endswith(('，', '。', '！', '？', '"', '"', '…', '……')):
                            content += "……"

                        # 创建章节记录
                        chapter = Chapter(
                            story_id=story.id,
                            chapter_number=1,
                            title=title,
                            content=content,
                            summary=content[:200] + "..."
                        )

                        self.db.add(chapter)
                        self.db.commit()
                        self.db.refresh(chapter)

                        # 添加默认选择选项
                        default_choices = [
                            "继续探索前方的道路",
                            "停下来仔细观察周围环境",
                            "寻找其他可能的路径"
                        ]

                        for choice_text in default_choices:
                            choice = Choice(
                                chapter_id=chapter.id,
                                choice_text=choice_text,
                                choice_type=ChoiceType.AI_GENERATED
                            )
                            self.db.add(choice)

                        # 更新故事状态
                        story.current_chapter_number = 1
                        story.chapter_summaries = [chapter.summary]
                        self.db.commit()

                        logger.info(f"章节恢复保存成功: {title}")

                        # 发送恢复保存成功信号
                        choices = self.get_chapter_choices(chapter.id)
                        yield {
                            "type": "recovery_complete",
                            "message": "章节已恢复保存",
                            "chapter": chapter.to_dict(),
                            "choices": [choice.to_dict() for choice in choices]
                        }

                    except Exception as e:
                        logger.error(f"章节恢复保存失败: {e}")
                        yield {
                            "type": "error",
                            "message": f"章节恢复保存失败: {str(e)}"
                        }
            elif not chapter_saved:
                # 如果没有收到任何有效内容
                yield {
                    "type": "error",
                    "message": "未收到有效的章节内容，无法保存"
                }

        except Exception as e:
            logger.error(f"章节生成流程异常: {e}")
            yield {"type": "error", "message": f"生成第一章失败: {str(e)}"}

    async def generate_next_chapter_stream(self, story_id: uuid.UUID, user_choice: str, provider: str = None):
        """根据用户选择流式生成下一章节"""
        try:
            logger.info(f"开始根据用户选择生成下一章节 - 故事ID: {story_id}, 选择: {user_choice}")

            # 获取故事信息
            story = self.get_story(story_id)
            if not story:
                raise Exception("故事不存在")

            # 获取章节摘要历史
            chapter_summaries = self.get_chapter_summaries(story_id)
            current_chapter_number = len(chapter_summaries) + 1

            logger.info(f"当前章节: {current_chapter_number}, 历史章节数: {len(chapter_summaries)}")

            # 获取世界观上下文
            worldview_context = ""
            if hasattr(story, 'worldview') and story.worldview:
                worldview_context = story.worldview.get_context_summary()

            # 构建故事数据，包含用户选择和章节历史
            story_data = {
                "title": story.title,
                "style": story.style.value,
                "current_chapter_number": current_chapter_number,
                "chapter_summaries": chapter_summaries,
                "user_choice": user_choice,  # 用户在上一章的选择
                "character_info": {
                    "主角": (story.worldview.worldview_data.get('main_character', {})
                           if hasattr(story, 'worldview') and story.worldview and story.worldview.worldview_data
                           else "主角")
                }
            }

            # 使用AI服务生成章节
            ai_service = AIService()

            async for chunk in ai_service.generate_chapter_stream(
                story_data,
                worldview_context=worldview_context,
                provider=provider,
                user_choice=user_choice  # 传递用户选择
            ):
                # 兼容处理：chunk可能是StreamChunk对象或字典
                if hasattr(chunk, 'type'):
                    # 新格式：StreamChunk对象
                    chunk_type = chunk.type
                    chunk_metadata = chunk.metadata or {}
                else:
                    # 旧格式：字典
                    chunk_type = chunk.get("type", "unknown")
                    chunk_metadata = chunk.get("data", {})

                if chunk_type == "chapter_complete":
                    # 保存章节到数据库
                    chapter_data = chunk_metadata
                    chapter = await self.save_chapter(
                        story_id=story_id,
                        chapter_number=current_chapter_number,
                        title=chapter_data.get("title", f"第{current_chapter_number}章"),
                        content=chapter_data.get("content", ""),
                        choices=chapter_data.get("choices", [])
                    )

                    # 更新故事的当前章节和章节摘要
                    story.current_chapter_number = current_chapter_number

                    # 更新章节摘要列表
                    updated_summaries = self.get_chapter_summaries(story_id)
                    story.chapter_summaries = [summary["summary"] for summary in updated_summaries]

                    self.db.commit()

                    logger.info(f"章节 {current_chapter_number} 生成并保存完成，摘要已更新")

                elif chunk_type == "complete":
                    # 处理完成信号，保存章节数据
                    logger.info("收到完成信号，开始保存到数据库")
                    chapter_data = chunk_metadata
                    chapter = await self.save_chapter(
                        story_id=story_id,
                        chapter_number=current_chapter_number,
                        title=chapter_data.get("title", f"第{current_chapter_number}章"),
                        content=chapter_data.get("content", ""),
                        choices=chapter_data.get("choices", [])
                    )

                    # 更新故事的当前章节和章节摘要
                    story.current_chapter_number = current_chapter_number

                    # 更新章节摘要列表
                    updated_summaries = self.get_chapter_summaries(story_id)
                    story.chapter_summaries = [summary["summary"] for summary in updated_summaries]

                    self.db.commit()

                    logger.info(f"章节 {current_chapter_number} 生成并保存完成，摘要已更新")

                    # 发送包含章节ID的完成信号
                    yield {
                        "type": "chapter_complete",
                        "data": {
                            "chapter_id": str(chapter.id),
                            "title": chapter_data.get("title", f"第{current_chapter_number}章"),
                            "content": chapter_data.get("content", ""),
                            "choices": chapter_data.get("choices", [])
                        }
                    }

                # 确保返回字典格式
                if hasattr(chunk, 'type'):
                    # StreamChunk对象转换为字典
                    yield {
                        "type": chunk.type,
                        "content": chunk.content,
                        "metadata": chunk.metadata
                    }
                else:
                    # 已经是字典格式
                    yield chunk

        except Exception as e:
            logger.error(f"根据用户选择生成下一章节失败: {e}")
            yield {"type": "error", "message": f"生成失败: {str(e)}"}

    async def save_chapter(self, story_id: str, chapter_number: int, title: str, content: str, choices: List[str]) -> Chapter:
        """保存章节到数据库"""
        try:
            # 生成真正的章节摘要
            logger.info(f"正在为章节 {chapter_number} 生成摘要...")
            try:
                summary = await self.generate_chapter_summary(content)
                logger.info(f"章节摘要生成成功: {summary[:50]}...")
            except Exception as e:
                logger.error(f"生成章节摘要失败: {e}，使用默认摘要")
                # 如果摘要生成失败，使用更智能的默认摘要
                summary = self._create_fallback_summary(content, title)

            # 创建章节记录
            chapter = Chapter(
                story_id=story_id,
                chapter_number=chapter_number,
                title=title,
                content=content,
                summary=summary
            )

            self.db.add(chapter)
            self.db.commit()
            self.db.refresh(chapter)

            # 保存选择选项
            for choice_text in choices:
                choice = Choice(
                    chapter_id=chapter.id,
                    choice_text=choice_text,
                    choice_type=ChoiceType.AI_GENERATED
                )
                self.db.add(choice)

            self.db.commit()

            # 更新故事的章节摘要
            story = self.get_story(story_id)
            if story:
                summaries = story.chapter_summaries or []
                summaries.append(summary)
                story.chapter_summaries = summaries
                story.current_chapter_number = chapter_number
                self.db.commit()

            logger.info(f"章节 {chapter_number} 保存成功: {title}")
            return chapter

        except Exception as e:
            self.db.rollback()
            logger.error(f"保存章节失败: {e}")
            raise e

    def _create_fallback_summary(self, content: str, title: str) -> str:
        """创建备用摘要（当AI摘要生成失败时使用）"""
        # 提取内容的关键句子，而不是简单截取开头
        sentences = content.replace('。', '。\n').replace('！', '！\n').replace('？', '？\n').split('\n')
        sentences = [s.strip() for s in sentences if s.strip() and len(s.strip()) > 10]

        if len(sentences) >= 3:
            # 取第一句、中间一句和最后一句
            first = sentences[0]
            middle = sentences[len(sentences)//2]
            last = sentences[-1]
            return f"{first} {middle} {last}"
        elif len(sentences) >= 2:
            return f"{sentences[0]} {sentences[-1]}"
        elif len(sentences) >= 1:
            return sentences[0]
        else:
            return f"{title}：本章内容精彩，情节发展引人入胜。"

    def get_chapter(self, chapter_id: uuid.UUID) -> Optional[Chapter]:
        """获取章节详情"""
        return self.db.query(Chapter).filter(Chapter.id == chapter_id).first()
    
    def get_chapter_choices(self, chapter_id: uuid.UUID) -> List[Choice]:
        """获取章节的选择选项"""
        return self.db.query(Choice).filter(
            Choice.chapter_id == chapter_id
        ).all()

    def get_chapter_summaries(self, story_id: uuid.UUID) -> List[Dict]:
        """获取故事的章节摘要列表"""
        try:
            story_id_str = str(story_id) if isinstance(story_id, uuid.UUID) else story_id
            # 从数据库获取章节
            chapters = self.db.query(Chapter).filter(
                Chapter.story_id == story_id_str
            ).order_by(Chapter.chapter_number).all()

            summaries = []
            for chapter in chapters:
                # 生成章节摘要（取前200字符作为摘要）
                content = chapter.content or ""
                summary = content[:200] + "..." if len(content) > 200 else content

                # 获取章节的选择选项
                choices = self.get_chapter_choices(chapter.id)
                choice_texts = [choice.choice_text for choice in choices]

                summaries.append({
                    "chapter_number": chapter.chapter_number,
                    "title": chapter.title,
                    "summary": summary,
                    "choices": choice_texts
                })

            return summaries

        except Exception as e:
            logger.error(f"获取章节摘要失败: {e}")
            return []

    def save_user_choice(self, story_id: uuid.UUID, chapter_number: int, choice_text: str, choice_type: str = "ai_generated") -> bool:
        """保存用户在某章节的选择"""
        try:
            story_id_str = str(story_id) if isinstance(story_id, uuid.UUID) else story_id

            # 查找对应章节
            chapter = self.db.query(Chapter).filter(
                Chapter.story_id == story_id_str,
                Chapter.chapter_number == chapter_number
            ).first()

            if chapter:
                # 保存用户选择到Choice表
                choice_type_enum = ChoiceType.USER_CUSTOM if choice_type == "user_custom" else ChoiceType.AI_GENERATED

                # 创建新的选择记录
                choice = Choice(
                    chapter_id=chapter.id,
                    choice_text=choice_text,
                    choice_type=choice_type_enum,
                    is_selected=True
                )

                self.db.add(choice)
                self.db.commit()

                logger.info(f"用户在章节 {chapter_number} 选择了: {choice_text} (类型: {choice_type})")
                return True
            else:
                logger.warning(f"章节 {chapter_number} 不存在")
                return False

        except Exception as e:
            logger.error(f"保存用户选择失败: {e}")
            return False
    
    def delete_story(self, story_id: uuid.UUID) -> bool:
        """删除故事及其所有相关数据"""
        try:
            # 获取故事
            story = self.get_story(story_id)
            if not story:
                raise ValueError("故事不存在")
            
            # 由于模型中设置了cascade="all, delete-orphan"
            # 删除故事时会自动级联删除所有相关的章节和选择
            self.db.delete(story)
            self.db.commit()
            
            return True
        except Exception as e:
            self.db.rollback()
            raise e
    
