from sqlalchemy.orm import Session
from typing import Optional, Dict, Any
from app.models import WorldView, Story, StoryStyle
from app.services.ai_service import ai_service
from app.services.character_service import CharacterService
from app.utils.logger import logger
import uuid

class WorldViewService:
    def __init__(self, db: Session):
        self.db = db
        self.character_service = CharacterService(db)
    
    async def create_worldview(
        self, story_id: str, story_theme: str = None, provider: str = None
    ) -> WorldView:
        """为故事创建世界观框架（兼容旧接口）"""
        # 获取故事信息
        story = self.db.query(Story).filter(Story.id == story_id).first()
        if not story:
            raise ValueError("故事不存在")

        # 检查是否已存在世界观
        existing_worldview = self.db.query(WorldView).filter(
            WorldView.story_id == story_id
        ).first()

        if existing_worldview:
            raise ValueError("该故事已存在世界观框架")

        try:
            # 使用AI智能生成世界观框架（统一使用自动生成方法）
            worldview_data = await ai_service.generate_worldview_auto(
                story_title=story.title,
                story_style=story.style,
                provider=provider
            )

            # 继续原来的处理逻辑...
            # 这里应该有完整的世界观处理代码，但为了简化，我们先返回一个基本实现
            import json

            # 创建基本的世界观记录
            worldview = WorldView(
                story_id=story_id,
                worldview_data=worldview_data,
                summary=str(worldview_data.get('world_name', ''))
            )

            self.db.add(worldview)
            self.db.commit()
            self.db.refresh(worldview)

            return worldview

        except Exception as e:
            self.db.rollback()
            raise Exception(f"生成世界观失败: {str(e)}")

    async def create_worldview_auto(
        self, story_id: str, story_title: str, story_style: StoryStyle, provider: str = None
    ) -> WorldView:
        """智能创建世界观：AI根据标题和风格自动生成完整世界观"""
        # 检查是否已存在世界观
        existing_worldview = self.db.query(WorldView).filter(
            WorldView.story_id == story_id
        ).first()

        if existing_worldview:
            raise ValueError("该故事已存在世界观框架")

        try:
            # AI自动生成世界观，不需要用户提供主题
            worldview_data = await ai_service.generate_worldview_auto(
                story_title=story_title,
                story_style=story_style,
                provider=provider
            )

            # 创建世界观记录
            import json

            worldview = WorldView(
                story_id=story_id,
                worldview_data=worldview_data,
                summary=str(worldview_data.get('world_name', ''))
            )

            self.db.add(worldview)
            self.db.commit()
            self.db.refresh(worldview)

            # 🎭 创建角色档案
            try:
                logger.info(f"开始为故事 {story_id} 创建角色档案")
                character_profile = await self.character_service.create_character_profile(
                    story_id=uuid.UUID(story_id),
                    worldview_data=worldview_data,
                    story_style=story_style.value
                )
                logger.info(f"角色档案创建成功: {character_profile.name}")
            except Exception as e:
                logger.warning(f"角色档案创建失败，但世界观创建成功: {e}")
                # 不影响世界观创建的成功

            return worldview

        except Exception as e:
            self.db.rollback()
            raise Exception(f"智能生成世界观失败: {str(e)}")
            
            # 适配新的纯世界观数据结构
            import json

            # 验证世界观数据
            if not worldview_data or len(worldview_data) == 0:
                raise Exception("AI生成的世界观数据为空")

            # 提取完整的世界观数据
            def extract_field(data, *field_names):
                """从嵌套数据中提取字段值"""
                for field_name in field_names:
                    if field_name in data and data[field_name]:
                        return str(data[field_name])
                return ""

            def extract_nested_field(data, path, *field_names):
                """从嵌套路径中提取字段值"""
                current = data
                for key in path:
                    if isinstance(current, dict) and key in current:
                        current = current[key]
                    else:
                        return ""
                return extract_field(current, *field_names)

            # 提取基本信息
            world_name = (extract_field(worldview_data, 'world_name', 'worldName', '名称') or
                         extract_nested_field(worldview_data, ['world_info'], 'name', '名称') or
                         extract_nested_field(worldview_data, ['world_lore', 'world_basic_info'], 'name', '名称') or
                         "未知世界")

            world_type = (extract_field(worldview_data, 'world_type', 'worldType', 'type', '类型') or
                         extract_nested_field(worldview_data, ['world_info'], 'type', '类型') or
                         extract_nested_field(worldview_data, ['world_lore', 'world_basic_info'], 'type', '类型') or
                         "修仙世界")

            # 提取详细信息
            power_system = (extract_field(worldview_data, 'power_system', 'powerSystem', '力量体系') or
                           extract_nested_field(worldview_data, ['world_lore'], 'power_system', 'powerSystem', '力量体系') or
                           extract_nested_field(worldview_data, ['world_info'], 'power_system', 'powerSystem', '力量体系'))

            social_structure = (extract_field(worldview_data, 'social_structure', 'socialStructure', '社会结构') or
                               extract_nested_field(worldview_data, ['world_lore'], 'social_structure', 'socialStructure', '社会结构'))

            geography = (extract_field(worldview_data, 'geography', '地理环境') or
                        extract_nested_field(worldview_data, ['world_lore'], 'geography', '地理环境'))

            history_background = (extract_field(worldview_data, 'history_background', 'historyBackground', '历史背景') or
                                 extract_nested_field(worldview_data, ['world_lore'], 'history_background', 'historyBackground', '历史背景'))

            # 提取角色信息
            main_character = {}
            if 'main_character' in worldview_data:
                main_character = worldview_data['main_character']
            elif 'characters' in worldview_data and worldview_data['characters']:
                chars = worldview_data['characters']
                if isinstance(chars, list) and chars:
                    main_character = chars[0]
                elif isinstance(chars, dict):
                    main_character = chars

            # 提取剧情信息
            main_plot = (extract_field(worldview_data, 'main_plot', 'mainPlot', '主线剧情') or
                        extract_nested_field(worldview_data, ['story_outline'], 'main_plot', 'mainPlot', '主线剧情'))

            conflict_setup = (extract_field(worldview_data, 'conflict_setup', 'conflictSetup', '冲突设置') or
                             extract_nested_field(worldview_data, ['story_outline'], 'conflict_setup', 'conflictSetup', '冲突设置'))

            # 提取风格信息
            narrative_style = extract_field(worldview_data, 'narrative_style', 'narrativeStyle', '叙述风格')
            tone_atmosphere = extract_field(worldview_data, 'tone_atmosphere', 'toneAtmosphere', '基调氛围')

            # 构建世界设定描述
            world_setting_desc = f"世界名称：{world_name}\n世界类型：{world_type}\n"

            # 武侠世界特有字段
            martial_arts = worldview_data.get('martial_arts', '')
            if martial_arts:
                world_setting_desc += f"武学体系：{martial_arts}\n"

            dynasty = worldview_data.get('dynasty', '')
            if dynasty:
                world_setting_desc += f"朝代背景：{dynasty}\n"

            major_sects = worldview_data.get('major_sects', '')
            if major_sects:
                world_setting_desc += f"主要门派：{major_sects}\n"

            rules = worldview_data.get('rules', '')
            if rules:
                world_setting_desc += f"江湖规则：{rules}\n"

            # 地理环境
            geography_data = worldview_data.get('geography', '')
            geography_desc = ""
            if geography_data:
                if isinstance(geography_data, dict):
                    geography_desc = f"地形类型：{geography_data.get('world_type', '')}\n"
                    geography_desc += f"主要区域：{', '.join(geography_data.get('major_regions', []))}\n"
                else:
                    geography_desc = f"地理环境：{geography_data}\n"

            # 社会结构
            social_structure_desc = ""
            civilization = worldview_data.get('civilization', {})
            if civilization:
                if isinstance(civilization, dict):
                    social_structure_desc = f"科技水平：{civilization.get('technology_level', '')}\n"
                    social_structure_desc += f"社会制度：{', '.join(civilization.get('social_systems', []))}\n"
                else:
                    social_structure_desc = f"社会结构：{civilization}\n"

            # 力量体系
            power_system_data = worldview_data.get('power_system', {})
            power_system_desc = ""
            if martial_arts:  # 优先使用武侠特有字段
                power_system_desc = f"武学体系：{martial_arts}\n"
            elif power_system_data:
                if isinstance(power_system_data, dict):
                    power_system_desc = f"体系名称：{power_system_data.get('system_name', '')}\n"
                    power_system_desc += f"力量来源：{power_system_data.get('power_source', '')}\n"
                else:
                    power_system_desc = f"力量体系：{power_system_data}\n"

            # 历史背景
            historical_context = worldview_data.get('historical_context', {})
            history_desc = ""
            if dynasty:  # 优先使用武侠特有字段
                history_desc = f"朝代背景：{dynasty}\n"
            elif historical_context:
                if isinstance(historical_context, dict):
                    history_desc = f"当前时代：{historical_context.get('current_era', '')}\n"
                    history_desc += f"主要历史时期：{', '.join(historical_context.get('major_eras', []))}\n"
                else:
                    history_desc = f"历史背景：{historical_context}\n"

            # 提取完整的世界观数据
            logger.info(f"开始解析世界观数据，顶层字段: {list(worldview_data.keys())}")

            # 如果是从文本提取的数据，需要特殊处理
            if 'extracted_content' in worldview_data:
                logger.info("检测到文本提取的数据，使用extracted_content")
                extracted_data = worldview_data['extracted_content']
                # 将提取的数据合并到主数据中
                worldview_data.update(extracted_data)
                logger.info(f"合并后的字段: {list(worldview_data.keys())}")

            def extract_field(data, *field_names):
                """从嵌套数据中提取字段值"""
                for field_name in field_names:
                    if field_name in data and data[field_name]:
                        value = data[field_name]
                        if isinstance(value, dict):
                            # 如果是字典，尝试提取描述字段
                            return str(value.get('description', value.get('desc', str(value))))
                        return str(value)
                return ""

            def extract_nested_field(data, path, *field_names):
                """从嵌套路径中提取字段值"""
                current = data
                for key in path:
                    if isinstance(current, dict) and key in current:
                        current = current[key]
                    else:
                        return ""
                return extract_field(current, *field_names)

            # 提取详细信息，优先使用AI返回的完整数据
            power_system_full = (extract_field(worldview_data, 'power_system', 'powerSystem', '力量体系', '修炼体系', '物理法则') or
                               extract_nested_field(worldview_data, ['世界基础信息'], 'power_system', 'powerSystem', '力量体系') or
                               extract_nested_field(worldview_data, ['物理法则'], 'description', '描述', 'basic_laws', 'basicLaws') or
                               extract_nested_field(worldview_data, ['world_background', 'power_system'], 'description', '描述') or
                               extract_nested_field(worldview_data, ['world_background'], 'power_system', 'powerSystem') or
                               power_system_desc)

            social_structure_full = (extract_field(worldview_data, 'social_structure', 'socialStructure', '社会结构', '社会体系', '文明体系') or
                                   extract_nested_field(worldview_data, ['社会结构'], 'description', '描述') or
                                   extract_nested_field(worldview_data, ['文明体系'], 'social_structure', 'socialStructure') or
                                   social_structure_desc)

            geography_full = (extract_field(worldview_data, 'geography', '地理环境', '地理设定', '世界地理') or
                            extract_nested_field(worldview_data, ['地理环境'], 'description', '描述') or
                            extract_nested_field(worldview_data, ['世界地理'], 'geography', '地理环境') or
                            geography_desc)

            history_background_full = (extract_field(worldview_data, 'history_background', 'historyBackground', '历史背景', '时代背景') or
                                     extract_nested_field(worldview_data, ['历史背景'], 'description', '描述') or
                                     extract_nested_field(worldview_data, ['世界基础信息'], 'era_background', 'eraBackground', '时代背景') or
                                     history_desc)

            # 提取角色信息
            main_character = {}
            if 'main_character' in worldview_data:
                main_character = worldview_data['main_character']
            elif 'characters' in worldview_data and worldview_data['characters']:
                chars = worldview_data['characters']
                if isinstance(chars, list) and chars:
                    main_character = chars[0]
                elif isinstance(chars, dict):
                    main_character = chars
            elif '主角设定' in worldview_data:
                main_character = worldview_data['主角设定']

            # 提取剧情信息
            main_plot = (extract_field(worldview_data, 'main_plot', 'mainPlot', '主线剧情', '故事大纲') or
                        extract_nested_field(worldview_data, ['故事大纲'], 'main_plot', 'mainPlot', '主线剧情'))

            conflict_setup = (extract_field(worldview_data, 'conflict_setup', 'conflictSetup', '冲突设置', '矛盾冲突') or
                             extract_nested_field(worldview_data, ['故事大纲'], 'conflict_setup', 'conflictSetup', '冲突设置'))

            # 提取风格信息
            narrative_style = extract_field(worldview_data, 'narrative_style', 'narrativeStyle', '叙述风格', '写作风格')
            tone_atmosphere = extract_field(worldview_data, 'tone_atmosphere', 'toneAtmosphere', '基调氛围', '故事氛围')

            # 提取主题
            story_themes = (worldview_data.get('story_themes', []) or
                          worldview_data.get('themes', []) or
                          worldview_data.get('主题', []) or
                          worldview_data.get('unique_features', []))
            if isinstance(story_themes, str):
                story_themes = [story_themes]
            elif isinstance(story_themes, list):
                # 确保列表中的所有元素都是字符串
                story_themes = [str(item) if not isinstance(item, str) else item for item in story_themes]
            else:
                story_themes = []

            # 构建完整的世界观JSON数据
            complete_worldview_data = {
                # 基础世界设定
                "world_setting": world_setting_desc,
                "power_system": power_system_full,
                "social_structure": social_structure_full,
                "geography": geography_full,
                "history_background": history_background_full,

                # 角色设定
                "main_character": main_character if isinstance(main_character, dict) else {},
                "supporting_characters": worldview_data.get('supporting_characters', []) or worldview_data.get('配角设定', []),
                "antagonists": worldview_data.get('antagonists', []) or worldview_data.get('反派设定', []),

                # 故事框架
                "main_plot": main_plot,
                "conflict_setup": conflict_setup,
                "story_themes": story_themes,

                # 风格特色
                "narrative_style": narrative_style,
                "tone_atmosphere": tone_atmosphere,

                # 保留AI返回的原始数据（用于后续扩展）
                "raw_ai_data": worldview_data
            }

            # 生成简要描述
            summary = world_setting_desc
            if power_system_full:
                summary += f"\n\n力量体系：{power_system_full[:200]}..."

            # 创建世界观记录（使用新的JSON结构）
            worldview = WorldView(
                story_id=story_id,
                worldview_data=complete_worldview_data,
                summary=summary
            )
            
            self.db.add(worldview)
            self.db.commit()
            self.db.refresh(worldview)
            
            return worldview
            
        except Exception as e:
            self.db.rollback()
            raise Exception(f"生成世界观失败: {str(e)}")
    
    def get_worldview(self, story_id: str) -> Optional[WorldView]:
        """获取故事的世界观框架"""
        return self.db.query(WorldView).filter(
            WorldView.story_id == story_id
        ).first()
    
    def get_worldview_by_id(self, worldview_id: str) -> Optional[WorldView]:
        """根据ID获取世界观"""
        return self.db.query(WorldView).filter(
            WorldView.id == worldview_id
        ).first()
    
    async def update_worldview(self, worldview_id: str, update_data: Dict[str, Any]) -> WorldView:
        """更新世界观框架"""
        worldview = self.get_worldview_by_id(worldview_id)
        if not worldview:
            raise ValueError("世界观不存在")
        
        try:
            # 更新字段
            for field, value in update_data.items():
                if hasattr(worldview, field):
                    setattr(worldview, field, value)
            
            self.db.commit()
            self.db.refresh(worldview)
            
            return worldview
            
        except Exception as e:
            self.db.rollback()
            raise Exception(f"更新世界观失败: {str(e)}")
    
    def delete_worldview(self, worldview_id: str) -> bool:
        """删除世界观框架"""
        try:
            worldview = self.get_worldview_by_id(worldview_id)
            if not worldview:
                raise ValueError("世界观不存在")
            
            self.db.delete(worldview)
            self.db.commit()
            
            return True
            
        except Exception as e:
            self.db.rollback()
            raise e
    
    async def regenerate_worldview(self, story_id: str, story_theme: str = None) -> WorldView:
        """重新生成世界观框架"""
        # 删除现有世界观
        existing_worldview = self.get_worldview(story_id)
        if existing_worldview:
            self.delete_worldview(existing_worldview.id)
        
        # 创建新的世界观
        return await self.create_worldview(story_id, story_theme)
    
    def add_character(self, worldview_id: str, character_data: Dict[str, Any], character_type: str = 'supporting') -> WorldView:
        """添加角色到世界观"""
        worldview = self.get_worldview_by_id(worldview_id)
        if not worldview:
            raise ValueError("世界观不存在")

        try:
            # 获取当前的世界观数据
            worldview_data = worldview.worldview_data or {}

            if character_type == 'supporting':
                characters = worldview_data.get('supporting_characters', [])
                if isinstance(characters, str):
                    import json
                    try:
                        characters = json.loads(characters)
                    except:
                        characters = []
                characters.append(character_data)
                worldview_data['supporting_characters'] = characters
            elif character_type == 'antagonist':
                antagonists = worldview_data.get('antagonists', [])
                if isinstance(antagonists, str):
                    import json
                    try:
                        antagonists = json.loads(antagonists)
                    except:
                        antagonists = []
                antagonists.append(character_data)
                worldview_data['antagonists'] = antagonists
            else:
                raise ValueError("不支持的角色类型")

            # 更新世界观数据
            worldview.worldview_data = worldview_data

            self.db.commit()
            self.db.refresh(worldview)

            return worldview

        except Exception as e:
            self.db.rollback()
            raise Exception(f"添加角色失败: {str(e)}")
    
    def update_main_character(self, worldview_id: str, character_data: Dict[str, Any]) -> WorldView:
        """更新主角信息"""
        worldview = self.get_worldview_by_id(worldview_id)
        if not worldview:
            raise ValueError("世界观不存在")

        try:
            # 获取当前的世界观数据
            worldview_data = worldview.worldview_data or {}

            # 合并主角数据
            main_character = worldview_data.get('main_character', {})
            if isinstance(main_character, str):
                import json
                try:
                    main_character = json.loads(main_character)
                except:
                    main_character = {}
            main_character.update(character_data)
            worldview_data['main_character'] = main_character

            # 更新世界观数据
            worldview.worldview_data = worldview_data

            self.db.commit()
            self.db.refresh(worldview)

            return worldview

        except Exception as e:
            self.db.rollback()
            raise Exception(f"更新主角失败: {str(e)}")