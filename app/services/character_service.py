"""
角色档案管理服务
基于独立角色数据表的完整角色管理系统
"""

import uuid
from typing import Dict, Any, Optional, List
from sqlalchemy.orm import Session
from app.models.story import Story
from app.models.character import Character, CharacterRelationship, CharacterEvent
from app.services.ai_service import AIService
from app.utils.logger import get_logger

logger = get_logger(__name__)

class CharacterService:
    """角色档案管理服务 - 基于独立角色数据表"""

    def __init__(self, db: Session):
        self.db = db
        self.ai_service = AIService()

    async def create_protagonist(
        self,
        story_id: str,
        worldview_data: Dict[str, Any],
        story_style: str
    ) -> Character:
        """基于世界观信息创建主角"""
        try:
            logger.info(f"开始为故事 {story_id} 创建主角")

            # 🎯 使用结构化输出生成角色档案（无需解析）
            character_profile = await self.ai_service.generate_character_profile_structured(
                worldview_data=worldview_data,
                story_style=story_style
            )

            # ✅ 直接使用结构化输出创建角色记录
            character = Character(
                story_id=story_id,
                name=character_profile.name,
                role_type='protagonist',
                personality_traits=character_profile.personality_traits,
                speaking_style=character_profile.speaking_style,
                background=character_profile.background,
                appearance=character_profile.appearance,
                core_abilities=character_profile.core_abilities,
                growth_goals=character_profile.growth_goals,
                key_relationships=character_profile.key_relationships,
                current_state=character_profile.current_state,
                current_location='起始地点',
                current_mood='平静',
                health_status='良好',
                power_level=1,
                character_arc_stage='初期'
            )

            # 保存到数据库
            self.db.add(character)
            self.db.commit()
            self.db.refresh(character)

            logger.info(f"✅ 主角创建成功: {character.name} (ID: {character.id}) - 使用结构化输出")
            return character

        except Exception as e:
            self.db.rollback()
            logger.error(f"创建主角失败: {e}")
            raise Exception(f"创建主角失败: {str(e)}")
    
    def get_protagonist(self, story_id: str) -> Optional[Character]:
        """获取故事的主角"""
        try:
            character = self.db.query(Character).filter(
                Character.story_id == story_id,
                Character.role_type == 'protagonist',
                Character.is_active == True
            ).first()

            return character

        except Exception as e:
            logger.error(f"获取主角失败: {e}")
            return None

    def get_all_characters(self, story_id: str) -> List[Character]:
        """获取故事的所有角色"""
        try:
            characters = self.db.query(Character).filter(
                Character.story_id == story_id,
                Character.is_active == True
            ).order_by(Character.role_type, Character.created_at).all()

            return characters

        except Exception as e:
            logger.error(f"获取角色列表失败: {e}")
            return []
    
    async def update_character_state(
        self,
        character_id: str,
        new_state: str,
        location: str = None,
        mood: str = None,
        health: str = None
    ) -> bool:
        """动态更新角色状态"""
        try:
            character = self.db.query(Character).filter(Character.id == character_id).first()
            if not character:
                return False

            # 记录状态变化前的信息
            old_state = character.current_state

            # 更新状态
            character.current_state = new_state
            if location:
                character.current_location = location
            if mood:
                character.current_mood = mood
            if health:
                character.health_status = health

            self.db.commit()

            # 记录状态变化事件
            await self._record_character_event(
                character_id=character_id,
                event_type='state_change',
                event_description=f'状态从"{old_state}"变更为"{new_state}"',
                state_before=old_state,
                state_after=new_state
            )

            logger.info(f"角色状态更新成功: {character.name} -> {new_state}")
            return True

        except Exception as e:
            self.db.rollback()
            logger.error(f"更新角色状态失败: {e}")
            return False

    async def update_character_power_level(
        self,
        character_id: str,
        new_level: int,
        reason: str = ""
    ) -> bool:
        """更新角色实力等级"""
        try:
            character = self.db.query(Character).filter(Character.id == character_id).first()
            if not character:
                return False

            old_level = character.power_level
            character.power_level = new_level
            self.db.commit()

            # 记录实力提升事件
            await self._record_character_event(
                character_id=character_id,
                event_type='power_growth',
                event_description=f'实力从等级{old_level}提升到{new_level}。原因：{reason}',
                impact_level=4 if new_level > old_level else 2
            )

            logger.info(f"角色实力更新成功: {character.name} -> 等级{new_level}")
            return True

        except Exception as e:
            self.db.rollback()
            logger.error(f"更新角色实力失败: {e}")
            return False
    
    def _build_character_prompt(self, worldview_data: Dict[str, Any], story_style: str) -> str:
        """构建角色档案生成提示词"""
        # 这里使用文档中的 CHARACTER_PROFILE_PROMPT
        return f"""
基于以下世界观信息，为主角创建详细的角色档案：

世界观信息：
世界设定：{worldview_data.get('world_setting', '')}
力量体系：{worldview_data.get('power_system', '')}
主角描述：{worldview_data.get('main_character', '')}
故事风格：{story_style}
故事主题：{worldview_data.get('story_themes', '')}

请按以下格式输出角色档案：

角色姓名：[具体的中文姓名，符合{story_style}风格]
性格特点：[3-5个核心性格特征，用逗号分隔]
说话方式：[描述角色的语言风格和表达习惯]
背景设定：[角色的出身、经历、当前处境]
外貌特征：[简洁的外貌描述，突出特色]
核心能力：[角色的主要能力或天赋]
成长目标：[角色想要达成的目标]
关键关系：[与其他重要角色的关系]

**角色设计要求：**
1. 姓名必须是具体的中文姓名，不能使用"主角"、"我"等代称
2. 性格要有明显的优点和缺点，避免完美人设
3. 背景设定必须与世界观设定完全吻合
4. 说话方式要有明显的个人特色
5. 成长目标要能支撑整个故事的发展
"""
    
    def _parse_character_data(self, character_text: str) -> Dict[str, Any]:
        """解析AI返回的角色信息"""
        # 简单的文本解析逻辑，后续可以优化为更智能的解析
        lines = character_text.strip().split('\n')
        character_data = {}
        
        for line in lines:
            if '：' in line:
                key, value = line.split('：', 1)
                key = key.strip()
                value = value.strip()
                
                if key == '角色姓名':
                    character_data['name'] = value
                elif key == '性格特点':
                    character_data['personality_traits'] = value
                elif key == '说话方式':
                    character_data['speaking_style'] = value
                elif key == '背景设定':
                    character_data['background'] = value
                elif key == '外貌特征':
                    character_data['appearance'] = value
                elif key == '核心能力':
                    character_data['core_abilities'] = value
                elif key == '成长目标':
                    character_data['growth_goals'] = value
                elif key == '关键关系':
                    character_data['key_relationships'] = value
        
        # 设置默认的当前状态
        character_data['current_state'] = '故事开始，准备踏上冒险之路'
        
        return character_data
    
    async def _record_character_event(
        self,
        character_id: str,
        event_type: str,
        event_description: str,
        chapter_id: str = None,
        impact_level: int = 3,
        state_before: str = None,
        state_after: str = None
    ):
        """记录角色事件"""
        try:
            event = CharacterEvent(
                character_id=character_id,
                chapter_id=chapter_id,
                event_type=event_type,
                event_description=event_description,
                impact_level=impact_level,
                state_before=state_before,
                state_after=state_after
            )

            self.db.add(event)
            self.db.commit()

        except Exception as e:
            logger.error(f"记录角色事件失败: {e}")

    def get_character_context_for_generation(self, story_id: str) -> str:
        """获取用于章节生成的角色上下文信息"""
        try:
            protagonist = self.get_protagonist(story_id)
            if not protagonist:
                return ""

            return f"""
**主角信息**：
{protagonist.get_profile_summary()}

**当前动态状态**：
{protagonist.get_dynamic_context()}
"""
        except Exception as e:
            logger.error(f"获取角色上下文失败: {e}")
            return ""
