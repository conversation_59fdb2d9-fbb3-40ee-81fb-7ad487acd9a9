"""
纯粹的世界观模型 - 只描述世界本身，不包含故事元素
"""

from typing import List, Optional
from pydantic import BaseModel, Field


class PhysicalLaws(BaseModel):
    """物理法则"""
    basic_laws: List[str] = Field(description="基础物理定律，如重力、时间流逝等")
    special_phenomena: List[str] = Field(description="特殊现象，如魔法、超能力的基本规则")
    limitations: List[str] = Field(description="物理限制和约束")


class Geography(BaseModel):
    """地理环境"""
    world_type: str = Field(description="世界类型：单一星球、星际、多维度等")
    major_regions: List[str] = Field(description="主要地理区域")
    climate_patterns: List[str] = Field(description="气候模式")
    natural_resources: List[str] = Field(description="自然资源分布")
    dangerous_areas: List[str] = Field(description="危险区域或禁地")


class Civilization(BaseModel):
    """文明体系"""
    technology_level: str = Field(description="整体科技水平")
    social_systems: List[str] = Field(description="社会制度类型")
    economic_models: List[str] = Field(description="经济模式")
    cultural_elements: List[str] = Field(description="文化特征")
    languages: List[str] = Field(description="主要语言")


class PowerSystem(BaseModel):
    """力量体系（如果存在）"""
    system_name: str = Field(description="力量体系名称")
    power_source: str = Field(description="力量来源")
    manifestation: List[str] = Field(description="力量表现形式")
    acquisition_methods: List[str] = Field(description="获得方式")
    limitations: List[str] = Field(description="限制条件")
    levels: List[str] = Field(description="等级划分")


class HistoricalContext(BaseModel):
    """历史背景"""
    major_eras: List[str] = Field(description="主要历史时期")
    significant_events: List[str] = Field(description="重大历史事件")
    current_era: str = Field(description="当前时代特征")
    ongoing_trends: List[str] = Field(description="当前发展趋势")


class WorldRules(BaseModel):
    """世界运行规则"""
    fundamental_principles: List[str] = Field(description="基本原则")
    cause_effect_patterns: List[str] = Field(description="因果关系模式")
    taboos_or_absolutes: List[str] = Field(description="禁忌或绝对规则")


class PureWorldView(BaseModel):
    """纯粹的世界观模型"""
    world_name: str = Field(description="世界名称")
    world_type: str = Field(description="世界类型：现实、奇幻、科幻等")
    
    physical_laws: PhysicalLaws = Field(description="物理法则")
    geography: Geography = Field(description="地理环境")
    civilization: Civilization = Field(description="文明体系")
    power_system: Optional[PowerSystem] = Field(
        default=None, 
        description="力量体系（如魔法、修仙等，可选）"
    )
    historical_context: HistoricalContext = Field(description="历史背景")
    world_rules: WorldRules = Field(description="世界运行规则")
    
    unique_features: List[str] = Field(description="世界独特特征")
    mysteries: List[str] = Field(description="世界未解之谜，为故事发展留空间")


class WorldViewGenerationRequest(BaseModel):
    """世界观生成请求"""
    world_type: str = Field(description="世界类型：现实、奇幻、科幻、修仙、武侠等")
    complexity_level: str = Field(
        default="medium",
        description="复杂度：simple(简单), medium(中等), complex(复杂)"
    )
    special_elements: Optional[List[str]] = Field(
        default=None,
        description="特殊元素要求，如魔法、科技、武功等"
    )
    inspiration_sources: Optional[List[str]] = Field(
        default=None,
        description="灵感来源，如特定作品、历史时期等"
    )
    tone: Optional[str] = Field(
        default=None,
        description="世界基调：黑暗、光明、中性等"
    )
