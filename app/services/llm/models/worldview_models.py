"""
世界观生成相关的结构化输出模型
"""

from typing import List, Optional

from pydantic import BaseModel, Field


class CharacterTemplate(BaseModel):
    """角色模板"""
    name: str = Field(description="角色名称")
    role: str = Field(description="角色定位：主角、配角、反派等")
    background: str = Field(description="角色背景故事")
    personality: str = Field(description="性格特征")
    abilities: List[str] = Field(description="特殊能力或技能")
    relationships: str = Field(
        default="",
        description="与其他角色的关系描述"
    )


class PowerSystem(BaseModel):
    """力量体系"""
    system_name: str = Field(description="力量体系名称")
    levels: List[str] = Field(description="等级划分")
    progression_method: str = Field(description="提升方式")
    special_abilities: List[str] = Field(description="特殊能力列表")
    limitations: List[str] = Field(description="限制和约束")


class WorldSetting(BaseModel):
    """世界设定"""
    world_name: str = Field(description="世界名称")
    time_period: str = Field(description="时代背景")
    geography: str = Field(description="地理环境描述")
    major_locations: List[str] = Field(description="主要地点列表")
    social_structure: str = Field(description="社会结构")
    technology_level: str = Field(description="科技水平")
    magic_or_special_elements: Optional[str] = Field(
        default=None,
        description="魔法或特殊元素"
    )


class WorldViewResponse(BaseModel):
    """世界观生成响应模型"""
    world_setting: WorldSetting = Field(description="世界基础设定")
    power_system: PowerSystem = Field(description="力量体系")
    main_character: CharacterTemplate = Field(description="主角设定")
    supporting_characters: List[CharacterTemplate] = Field(
        description="主要配角设定"
    )
    antagonists: List[CharacterTemplate] = Field(
        description="主要反派设定"
    )
    central_conflict: str = Field(description="核心矛盾冲突")
    story_themes: List[str] = Field(description="故事主题")
    plot_hooks: List[str] = Field(description="情节钩子，用于推动故事发展")
    world_rules: List[str] = Field(description="世界运行规则")
    generation_notes: Optional[str] = Field(
        default=None,
        description="生成备注信息"
    )


class WorldViewGenerationRequest(BaseModel):
    """世界观生成请求模型"""
    story_style: str = Field(description="小说风格：修仙、武侠、科幻等")
    story_theme: Optional[str] = Field(
        default=None,
        description="故事主题或核心概念"
    )
    protagonist_concept: Optional[str] = Field(
        default=None,
        description="主角概念或设定"
    )
    world_scope: str = Field(
        default="medium",
        description="世界规模：small(小型), medium(中型), large(大型)"
    )
    complexity_level: str = Field(
        default="medium",
        description="复杂度：simple(简单), medium(中等), complex(复杂)"
    )
    special_requirements: Optional[str] = Field(
        default=None,
        description="特殊要求"
    )
