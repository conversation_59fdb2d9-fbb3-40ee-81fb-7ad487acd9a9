"""
章节生成相关的结构化输出模型
"""

from enum import Enum
from typing import List, Optional

from pydantic import BaseModel, Field


class ChoiceType(str, Enum):
    """选择类型"""
    ACTION = "action"  # 行动型选择
    DIALOGUE = "dialogue"  # 对话型选择
    DECISION = "decision"  # 决策型选择
    EXPLORATION = "exploration"  # 探索型选择


class ChoiceOption(BaseModel):
    """选择选项模型"""
    choice_text: str = Field(
        description="选择的具体文本内容，简洁明了，不超过30字"
    )
    choice_type: ChoiceType = Field(
        description="选择的类型分类"
    )
    consequence_hint: Optional[str] = Field(
        default=None,
        description="选择可能带来的后果提示，可选"
    )
    risk_level: int = Field(
        default=1,
        ge=1,
        le=3,
        description="风险等级：1=低风险，2=中风险，3=高风险"
    )


class ChapterContent(BaseModel):
    """章节内容模型"""
    title: str = Field(
        description="章节标题，简洁有吸引力"
    )
    content: str = Field(
        description="章节正文内容，严格控制在2500-3000字之间，情节完整连贯，结尾设置悬念引导用户选择"
    )
    summary: str = Field(
        description="章节内容摘要，100-200字，概括主要情节"
    )
    key_events: List[str] = Field(
        description="本章节的关键事件列表，用于后续情节参考"
    )
    character_development: Optional[str] = Field(
        default=None,
        description="角色发展变化描述，记录主要角色的状态变化"
    )


class ChapterResponse(BaseModel):
    """章节生成完整响应模型"""
    chapter: ChapterContent = Field(
        description="生成的章节内容"
    )
    choices: List[ChoiceOption] = Field(
        description="章节结尾的选择选项，必须包含3个不同类型的选择"
    )
    world_consistency_check: bool = Field(
        default=True,
        description="世界观一致性检查结果"
    )
    generation_notes: Optional[str] = Field(
        default=None,
        description="生成过程的备注信息"
    )


class ChapterGenerationRequest(BaseModel):
    """章节生成请求模型"""
    story_style: str = Field(
        description="小说风格：修仙、武侠、科幻等"
    )
    story_title: str = Field(
        description="故事标题"
    )
    current_chapter_number: int = Field(
        description="当前章节号"
    )
    previous_chapters_summary: List[str] = Field(
        default=[],
        description="前面章节的摘要列表"
    )
    character_info: str = Field(
        default="",
        description="角色信息和状态描述"
    )
    world_context: str = Field(
        description="世界观背景描述"
    )
    previous_choice: Optional[str] = Field(
        default=None,
        description="用户在上一章节做出的选择"
    )
    special_requirements: Optional[str] = Field(
        default=None,
        description="特殊要求或约束条件"
    )
