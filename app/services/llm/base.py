"""
LLM提供商抽象基类

定义所有LLM提供商必须实现的接口，确保统一的调用方式。
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, AsyncGenerator, Optional, List
from pydantic import BaseModel
from enum import Enum


class LLMProviderType(Enum):
    """LLM提供商类型"""
    GEMINI = "gemini"
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    SILICONFLOW = "siliconflow"


class StreamChunk(BaseModel):
    """流式输出数据块"""
    type: str  # "title", "content", "complete", "error"
    content: str
    metadata: Optional[Dict[str, Any]] = None


class LLMProvider(ABC):
    """LLM提供商抽象基类"""
    
    def __init__(self, model_name: str, **kwargs):
        self.model_name = model_name
        self.config = kwargs
    
    @property
    @abstractmethod
    def provider_type(self) -> LLMProviderType:
        """返回提供商类型"""
        pass
    
    @abstractmethod
    async def generate_stream(
        self, 
        prompt: str, 
        **kwargs
    ) -> AsyncGenerator[StreamChunk, None]:
        """
        流式生成文本内容
        
        Args:
            prompt: 输入提示词
            **kwargs: 额外参数
            
        Yields:
            StreamChunk: 流式数据块
        """
        pass
    
    @abstractmethod
    async def generate_structured_output(
        self, 
        prompt: str, 
        output_model: BaseModel,
        **kwargs
    ) -> BaseModel:
        """
        生成结构化输出
        
        Args:
            prompt: 输入提示词
            output_model: 输出模型类
            **kwargs: 额外参数
            
        Returns:
            BaseModel: 结构化输出对象
        """
        pass
    
    @abstractmethod
    async def check_availability(self) -> bool:
        """
        检查提供商可用性
        
        Returns:
            bool: 是否可用
        """
        pass
    
    @abstractmethod
    def estimate_cost(self, prompt: str, max_tokens: int = 1000) -> float:
        """
        估算调用成本
        
        Args:
            prompt: 输入提示词
            max_tokens: 最大输出token数
            
        Returns:
            float: 预估成本(美元)
        """
        pass
    
    def get_model_info(self) -> Dict[str, Any]:
        """
        获取模型信息
        
        Returns:
            Dict: 模型信息
        """
        return {
            "provider": self.provider_type.value,
            "model": self.model_name,
            "config": self.config
        }


class LLMError(Exception):
    """LLM相关错误基类"""
    pass


class LLMProviderError(LLMError):
    """LLM提供商错误"""
    pass


class LLMRateLimitError(LLMError):
    """LLM速率限制错误"""
    pass


class LLMQuotaExceededError(LLMError):
    """LLM配额超限错误"""
    pass
