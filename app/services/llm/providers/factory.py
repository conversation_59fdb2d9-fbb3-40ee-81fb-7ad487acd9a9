"""
LLM提供商工厂

负责创建和管理不同的LLM提供商实例。
"""

from typing import Dict, Any, Optional

from ..base import LLMProvider, LLMProviderType, LLMProviderError
from .gemini_provider import GeminiProvider
from .siliconflow_provider import SiliconFlowProvider
from app.config import settings
from app.utils.logger import get_logger

logger = get_logger(__name__)


class LLMProviderFactory:
    """LLM提供商工厂类"""
    
    _providers = {
        LLMProviderType.GEMINI: GeminiProvider,
        LLMProviderType.SILICONFLOW: SiliconFlowProvider,
    }
    
    @classmethod
    def create_provider(
        cls,
        provider_type: str | LLMProviderType,
        model_name: Optional[str] = None,
        **kwargs
    ) -> LLMProvider:
        """
        创建LLM提供商实例
        
        Args:
            provider_type: 提供商类型
            model_name: 模型名称
            **kwargs: 额外配置参数
            
        Returns:
            LLMProvider: 提供商实例
        """
        try:
            # 转换字符串为枚举
            if isinstance(provider_type, str):
                provider_type = LLMProviderType(provider_type)
            
            # 获取提供商类
            provider_class = cls._providers.get(provider_type)
            if not provider_class:
                raise LLMProviderError(f"Unsupported provider type: {provider_type}")
            
            # 设置默认模型名称
            if not model_name:
                model_name = cls._get_default_model(provider_type)
            
            # 创建提供商实例
            provider = provider_class(model_name=model_name, **kwargs)
            
            logger.info(f"Created LLM provider: {provider_type.value} with model: {model_name}")
            return provider
            
        except Exception as e:
            logger.error(f"Failed to create LLM provider: {e}")
            raise LLMProviderError(f"Provider creation failed: {e}")
    
    @classmethod
    def create_default_provider(cls, **kwargs) -> LLMProvider:
        """
        创建默认LLM提供商

        Returns:
            LLMProvider: 默认提供商实例
        """
        # 根据配置使用默认提供商
        default_provider = settings.default_llm_provider.lower()

        if default_provider == "siliconflow":
            return cls.create_provider(
                provider_type=LLMProviderType.SILICONFLOW,
                model_name=settings.siliconflow_model,
                **kwargs
            )
        else:
            # 默认使用Gemini
            return cls.create_provider(
                provider_type=LLMProviderType.GEMINI,
                model_name=settings.gemini_model,
                **kwargs
            )
    

    
    @classmethod
    def _get_default_model(cls, provider_type: LLMProviderType) -> str:
        """获取提供商的默认模型"""
        if provider_type == LLMProviderType.GEMINI:
            return settings.gemini_model
        elif provider_type == LLMProviderType.SILICONFLOW:
            return settings.siliconflow_model
        return ""
    
    @classmethod
    def get_available_providers(cls) -> Dict[str, Any]:
        """获取可用的提供商信息"""
        return {
            provider_type.value: {
                "class": provider_class.__name__,
                "description": provider_class.__doc__ or ""
            }
            for provider_type, provider_class in cls._providers.items()
        }
    
    @classmethod
    def register_provider(
        cls, 
        provider_type: LLMProviderType, 
        provider_class: type
    ):
        """
        注册新的提供商类型
        
        Args:
            provider_type: 提供商类型
            provider_class: 提供商类
        """
        if not issubclass(provider_class, LLMProvider):
            raise LLMProviderError("Provider class must inherit from LLMProvider")
        
        cls._providers[provider_type] = provider_class
        logger.info(f"Registered new LLM provider: {provider_type.value}")
