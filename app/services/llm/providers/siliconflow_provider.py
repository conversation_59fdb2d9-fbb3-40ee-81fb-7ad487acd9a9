"""
硅基流动提供商实现

使用硅基流动API提供LLM服务，支持文本生成、流式生成和结构化输出。
兼容OpenAI接口，提供高性价比的AI服务。
"""

from typing import Dict, Any, AsyncGenerator, Optional
from pydantic import BaseModel
import json
import asyncio

try:
    from openai import AsyncOpenAI
    OPENAI_AVAILABLE = True
except ImportError:
    AsyncOpenAI = None
    OPENAI_AVAILABLE = False

from ..base import LLMProvider, LLMProviderType, StreamChunk, LLMProviderError
from app.config import settings
from app.utils.logger import get_logger

logger = get_logger(__name__)


class SiliconFlowProvider(LLMProvider):
    """硅基流动LLM提供商"""
    
    def __init__(self, model_name: str = None, **kwargs):
        """初始化硅基流动提供商"""
        if not OPENAI_AVAILABLE:
            raise LLMProviderError("OpenAI package not available. Please install: pip install openai")
        
        # 使用配置中的模型名称或传入的模型名称
        model_name = model_name or settings.siliconflow_model
        super().__init__(model_name, **kwargs)
        
        # 验证API密钥
        if not settings.siliconflow_api_key:
            raise LLMProviderError("SiliconFlow API key not configured")
        
        try:
            # 初始化硅基流动客户端（使用OpenAI兼容接口）
            self._client = AsyncOpenAI(
                api_key=settings.siliconflow_api_key,
                base_url=settings.siliconflow_base_url
            )
            
            logger.info(f"SiliconFlow provider initialized with model: {self.model_name}")
            logger.info(f"Base URL: {settings.siliconflow_base_url}")
            
        except Exception as e:
            logger.error(f"Failed to initialize SiliconFlow client: {e}")
            raise LLMProviderError(f"SiliconFlow initialization failed: {e}")
    
    @property
    def provider_type(self) -> LLMProviderType:
        """返回提供商类型"""
        return LLMProviderType.SILICONFLOW
    
    async def generate_stream(
        self,
        prompt: str,
        **kwargs
    ) -> AsyncGenerator[StreamChunk, None]:
        """流式生成文本内容"""
        try:
            if not self._client:
                raise LLMProviderError("SiliconFlow client not initialized")

            logger.info(f"Starting SiliconFlow stream generation with model: {self.model_name}")
            logger.info(f"🎨 使用Temperature: {self.config.get('temperature', 0.7)}")

            # 使用OpenAI兼容的流式API
            response_stream = await self._client.chat.completions.create(
                model=self.model_name,
                messages=[{"role": "user", "content": prompt}],
                stream=True,
                max_tokens=self.config.get('max_tokens', 4096),  # 硅基流动最大支持4096
                temperature=self.config.get('temperature', 0.7),
            )

            total_content = ""
            chunk_count = 0
            
            async for chunk in response_stream:
                chunk_count += 1

                if chunk.choices and len(chunk.choices) > 0:
                    delta = chunk.choices[0].delta

                    # 处理推理模型的thinking内容（不输出给用户）
                    if hasattr(delta, 'reasoning_content') and delta.reasoning_content:
                        # 推理内容，记录但不输出
                        logger.debug(f"🧠 推理过程: {delta.reasoning_content[:100]}...")
                        continue

                    # 处理正常的内容输出
                    if delta.content:
                        total_content += delta.content
                        yield StreamChunk(
                            type="content",
                            content=delta.content,
                            metadata={
                                'model': self.model_name,
                                'provider': 'siliconflow',
                                'is_final': False,
                                'is_reasoning_model': self._is_reasoning_model()
                            }
                        )

                    # 检查是否完成
                    if chunk.choices[0].finish_reason:
                        break

            # 检查总内容
            if len(total_content.strip()) == 0:
                logger.error(f"SiliconFlow流式生成完成但内容为空，总chunk数: {chunk_count}")
                raise LLMProviderError("SiliconFlow流式生成返回空内容")

            # 发送最终chunk
            yield StreamChunk(
                type="complete",
                content="",
                metadata={
                    'model': self.model_name,
                    'provider': 'siliconflow',
                    'is_final': True,
                    'total_tokens': len(total_content)
                }
            )

            logger.info(f"SiliconFlow stream generation completed, total content length: {len(total_content)}")

        except Exception as e:
            logger.error(f"SiliconFlow stream generation failed: {e}")
            yield StreamChunk(
                type="error",
                content="",
                metadata={
                    'error': str(e),
                    'provider': 'siliconflow'
                }
            )
    
    async def generate_text(self, prompt: str, **kwargs) -> str:
        """生成文本内容（非流式）"""
        try:
            if not self._client:
                raise LLMProviderError("SiliconFlow client not initialized")

            logger.info(f"Starting SiliconFlow text generation with model: {self.model_name}")
            logger.info(f"🎨 使用Temperature: {self.config.get('temperature', 0.7)}")

            # 使用OpenAI兼容的API
            response = await self._client.chat.completions.create(
                model=self.model_name,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=self.config.get('max_tokens', 4096),  # 硅基流动最大支持4096
                temperature=self.config.get('temperature', 0.7),
            )

            if not response.choices or len(response.choices) == 0:
                raise LLMProviderError("SiliconFlow返回空响应")

            content = response.choices[0].message.content
            if not content:
                raise LLMProviderError("SiliconFlow返回空内容")

            logger.info(f"SiliconFlow text generation completed, content length: {len(content)}")
            return content

        except Exception as e:
            logger.error(f"SiliconFlow text generation failed: {e}")
            raise LLMProviderError(f"Text generation failed: {e}")
    
    async def generate_structured_output(
        self,
        prompt: str,
        output_model: BaseModel,
        **kwargs
    ) -> BaseModel:
        """生成结构化输出"""
        try:
            logger.info(f"Starting SiliconFlow structured output with model: {self.model_name}")

            # 添加JSON格式要求到提示词
            json_prompt = f"""{prompt}

请严格按照以下JSON格式返回结果：
{output_model.model_json_schema()}

只返回JSON，不要包含其他文字。"""

            # 使用文本生成API
            response_text = await self.generate_text(json_prompt, **kwargs)
            
            # 尝试解析JSON
            try:
                # 清理响应文本
                response_text = response_text.strip()
                if response_text.startswith('```json'):
                    response_text = response_text[7:]
                if response_text.endswith('```'):
                    response_text = response_text[:-3]
                response_text = response_text.strip()
                
                # 解析JSON
                json_data = json.loads(response_text)
                
                # 创建Pydantic模型实例
                result = output_model(**json_data)
                logger.info("SiliconFlow structured output completed successfully")
                return result
                
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse JSON response: {e}")
                logger.error(f"Response text: {response_text}")
                raise LLMProviderError(f"Invalid JSON response: {e}")
            except Exception as e:
                logger.error(f"Failed to create model instance: {e}")
                raise LLMProviderError(f"Model validation failed: {e}")

        except Exception as e:
            logger.error(f"SiliconFlow structured output failed: {e}")
            raise LLMProviderError(f"Structured output failed: {e}")
    
    def _is_reasoning_model(self) -> bool:
        """检查是否为推理模型"""
        reasoning_models = [
            "Qwen/QwQ-32B",
            "Qwen/Qwen3-8B",
            "Qwen/Qwen3-14B",
            "Qwen/Qwen3-32B",
            "Qwen/Qwen3-235B-A22B",
            "Qwen/Qwen3-235B-A22B-Thinking-2507",
            "deepseek-ai/DeepSeek-R1",
            "deepseek-ai/DeepSeek-R1-Distill-Qwen-7B",
            "deepseek-ai/DeepSeek-R1-Distill-Qwen-14B",
            "deepseek-ai/DeepSeek-R1-Distill-Qwen-32B",
            "THUDM/GLM-4.1V-9B-Thinking",
            "THUDM/GLM-Z1-Rumination-32B-0414"
        ]
        return any(model in self.model_name for model in reasoning_models)

    def estimate_cost(self, prompt: str, max_tokens: int = 1000) -> float:
        """估算调用成本"""
        try:
            # 硅基流动的定价（元/M tokens）
            model_pricing = {
                # 免费模型
                "Qwen/Qwen2.5-7B-Instruct": 0.0,
                "deepseek-ai/DeepSeek-R1-Distill-Qwen-7B": 0.0,
                "THUDM/GLM-4-9B-0414": 0.0,
                "Qwen/Qwen2.5-Coder-7B-Instruct": 0.0,
                "internlm/internlm2_5-7b-chat": 0.0,

                # 付费模型（输入和输出价格相同）
                "Qwen/Qwen2.5-14B-Instruct": 0.7,
                "deepseek-ai/DeepSeek-V2.5": 1.33,
                "Qwen/Qwen2.5-32B-Instruct": 1.26,
                "Qwen/Qwen2.5-72B-Instruct": 4.13,
                "deepseek-ai/DeepSeek-V3": 8.0,
                "deepseek-ai/DeepSeek-R1": 16.0,
            }

            # 获取模型价格（元/M tokens）
            price_per_m_tokens = model_pricing.get(self.model_name, 1.0)  # 默认1元/M tokens

            # 估算token数量（简单估算：1个中文字符≈1.5tokens，1个英文单词≈1.3tokens）
            prompt_tokens = len(prompt) * 1.2  # 简化估算
            total_tokens = prompt_tokens + max_tokens

            # 计算成本（元）
            cost_yuan = (total_tokens / 1_000_000) * price_per_m_tokens

            # 转换为美元（假设汇率7.2）
            cost_usd = cost_yuan / 7.2

            logger.debug(f"Cost estimation for {self.model_name}: ¥{cost_yuan:.6f} (${cost_usd:.6f})")
            return cost_usd

        except Exception as e:
            logger.error(f"Cost estimation failed: {e}")
            return 0.0

    async def check_availability(self) -> bool:
        """检查提供商可用性"""
        try:
            if not self._client:
                return False

            # 发送一个简单的测试请求
            response = await self._client.chat.completions.create(
                model=self.model_name,
                messages=[{"role": "user", "content": "Hello"}],
                max_tokens=10
            )

            return bool(response.choices and response.choices[0].message.content)

        except Exception as e:
            logger.error(f"SiliconFlow availability check failed: {e}")
            return False
