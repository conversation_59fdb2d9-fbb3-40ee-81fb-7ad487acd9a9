"""
Gemini提供商实现

使用新的Google GenAI SDK提供LLM服务，支持文本生成、流式生成和结构化输出。
"""

from typing import Dict, Any, AsyncGenerator, Optional
from pydantic import BaseModel
import asyncio

try:
    # 尝试新的 google-genai 包
    from google import genai
    GEMINI_SDK_TYPE = "google-genai"
    GEMINI_AVAILABLE = True
except ImportError:
    try:
        # 尝试 google-generativeai 包
        import google.generativeai as genai
        GEMINI_SDK_TYPE = "google-generativeai"
        GEMINI_AVAILABLE = True
    except ImportError:
        genai = None
        GEMINI_SDK_TYPE = None
        GEMINI_AVAILABLE = False

from ..base import LLMProvider, LLMProviderType, StreamChunk, LLMProviderError
from app.config import settings
from app.utils.logger import get_logger

logger = get_logger(__name__)


class GeminiProvider(LLMProvider):
    """Gemini提供商实现 - 使用新的Google GenAI SDK"""
    
    def __init__(self, model_name: str = None, **kwargs):
        model_name = model_name or settings.gemini_model
        super().__init__(model_name, **kwargs)
        self.api_key = kwargs.get('api_key', settings.gemini_api_key)
        self._client = None
        self._initialize_client()
    
    def _initialize_client(self):
        """初始化Gemini客户端"""
        if not GEMINI_AVAILABLE or genai is None:
            logger.error("Google GenAI SDK not available")
            raise LLMProviderError("Google GenAI SDK not installed")

        try:
            if not self.api_key:
                logger.warning("Gemini API key not configured")
                return

            # 根据SDK类型初始化客户端
            if GEMINI_SDK_TYPE == "google-genai":
                # 使用新的Google GenAI SDK
                self._client = genai.Client(api_key=self.api_key)
            elif GEMINI_SDK_TYPE == "google-generativeai":
                # 使用传统的 google-generativeai SDK
                genai.configure(api_key=self.api_key)
                self._client = genai

            logger.info(f"Gemini client initialized: {self.model_name} (SDK: {GEMINI_SDK_TYPE})")

        except Exception as e:
            logger.error(f"Failed to initialize Gemini client: {e}")
            raise LLMProviderError(f"Gemini initialization failed: {e}")
    
    @property
    def provider_type(self) -> LLMProviderType:
        """返回提供商类型"""
        return LLMProviderType.GEMINI
    
    async def generate_stream(
        self,
        prompt: str,
        **kwargs
    ) -> AsyncGenerator[StreamChunk, None]:
        """流式生成文本内容"""
        try:
            if not self._client:
                raise LLMProviderError("Gemini client not initialized")

            logger.info(f"Starting Gemini stream generation with model: {self.model_name}")

            # 根据SDK类型使用不同的流式生成方式
            if GEMINI_SDK_TYPE == "google-genai":
                # 使用新的Google GenAI SDK
                response_stream = self._client.models.generate_content_stream(
                    model=self.model_name,
                    contents=prompt,
                    config={
                        'max_output_tokens': 8192,
                        'temperature': self.config.get('temperature', 0.7),
                    }
                )
            elif GEMINI_SDK_TYPE == "google-generativeai":
                # 使用传统的 google-generativeai SDK
                model = genai.GenerativeModel(self.model_name)
                response_stream = model.generate_content(
                    prompt,
                    generation_config=genai.types.GenerationConfig(
                        max_output_tokens=8192,
                        temperature=self.config.get('temperature', 0.7),
                    ),
                    stream=True
                )
            else:
                raise LLMProviderError(f"Unknown SDK type: {GEMINI_SDK_TYPE}")

            # 直接迭代，不使用async for
            total_content = ""
            chunk_count = 0
            for chunk in response_stream:
                chunk_count += 1
                if chunk.text:
                    total_content += chunk.text
                    yield StreamChunk(
                        type="content",
                        content=chunk.text,
                        metadata={
                            'model': self.model_name,
                            'provider': 'gemini',
                            'is_final': False
                        }
                    )
                else:
                    # 记录空chunk
                    logger.warning(f"Gemini返回空chunk，chunk #{chunk_count}")

            # 检查总内容
            if len(total_content.strip()) == 0:
                logger.error(f"Gemini流式生成完成但内容为空，总chunk数: {chunk_count}")
                raise LLMProviderError("Gemini流式生成返回空内容")

            # 发送最终chunk
            yield StreamChunk(
                type="complete",
                content="",
                metadata={
                    'model': self.model_name,
                    'provider': 'gemini',
                    'is_final': True
                }
            )

            logger.info("Gemini stream generation completed")

        except Exception as e:
            logger.error(f"Gemini stream generation failed: {e}")
            raise LLMProviderError(f"Stream generation failed: {e}")
    
    async def generate_text(self, prompt: str, **kwargs) -> str:
        """生成文本内容"""
        try:
            if not self._client:
                raise LLMProviderError("Gemini client not initialized")

            logger.info(f"Starting Gemini text generation with model: {self.model_name}")
            logger.info(f"Current GEMINI_SDK_TYPE: {GEMINI_SDK_TYPE}")

            # 根据SDK类型使用不同的API调用方式
            if GEMINI_SDK_TYPE == "google-genai":
                # 使用新的Google GenAI SDK
                response = self._client.models.generate_content(
                    model=self.model_name,
                    contents=prompt,
                    config={
                        'max_output_tokens': 8192,
                        'temperature': self.config.get('temperature', 0.7),
                    }
                )
            elif GEMINI_SDK_TYPE == "google-generativeai":
                # 使用传统的 google-generativeai SDK
                model = genai.GenerativeModel(self.model_name)
                response = model.generate_content(
                    prompt,
                    generation_config=genai.types.GenerationConfig(
                        max_output_tokens=8192,
                        temperature=self.config.get('temperature', 0.7),
                    )
                )
            else:
                raise LLMProviderError(f"Unknown SDK type: {GEMINI_SDK_TYPE}")

            result = response.text
            logger.info(f"Gemini text generation completed: {len(result)} characters")

            # 检查空响应
            if not result or len(result.strip()) == 0:
                logger.warning("Gemini returned empty response")
                # 检查响应的其他信息
                if hasattr(response, 'candidates') and response.candidates:
                    for i, candidate in enumerate(response.candidates):
                        if hasattr(candidate, 'finish_reason'):
                            logger.warning(f"Candidate {i} finish reason: {candidate.finish_reason}")
                        if hasattr(candidate, 'safety_ratings'):
                            logger.warning(f"Candidate {i} safety ratings: {candidate.safety_ratings}")

                raise LLMProviderError("Gemini returned empty response - possible content filtering or API issue")

            return result

        except Exception as e:
            logger.error(f"Gemini text generation failed: {e}")
            raise LLMProviderError(f"Text generation failed: {e}")
    
    async def generate_structured_output(
        self,
        prompt: str,
        output_model: BaseModel,
        **kwargs
    ) -> BaseModel:
        """生成结构化输出 - 使用Gemini原生结构化输出"""
        try:
            logger.info(f"Starting Gemini structured output with model: {self.model_name}")

            # 使用同步API的结构化输出 - 直接传递Pydantic模型
            response = self._client.models.generate_content(
                model=self.model_name,
                contents=prompt,
                config={
                    'response_mime_type': 'application/json',
                    'response_schema': output_model,  # 直接传递Pydantic模型
                    'max_output_tokens': self.config.get('max_tokens', 3000),
                    'temperature': self.config.get('temperature', 0.7),
                }
            )

            # 检查响应内容
            logger.info(f"Gemini response text length: {len(response.text) if response.text else 0}")

            # 检查是否有parsed属性（新SDK自动解析）
            if hasattr(response, 'parsed') and response.parsed:
                result = response.parsed
                logger.info(f"Gemini structured output completed (parsed): {type(result).__name__}")
                return result
            elif response.text and response.text.strip():
                # 降级到JSON解析，使用更强的解析逻辑
                import json
                import re

                try:
                    # 首先尝试直接解析
                    data = json.loads(response.text)
                    result = output_model(**data)
                    logger.info(f"Gemini structured output completed (json): {type(result).__name__}")
                    return result
                except json.JSONDecodeError as je:
                    logger.warning(f"Direct JSON parsing failed: {je}")

                    # 尝试清理和修复JSON
                    try:
                        # 提取JSON部分（去除markdown标记等）
                        json_match = re.search(r'\{.*\}', response.text, re.DOTALL)
                        if json_match:
                            json_text = json_match.group()

                            # 尝试修复常见的JSON问题
                            json_text = self._fix_json_format(json_text)

                            data = json.loads(json_text)
                            result = output_model(**data)
                            logger.info(f"Gemini structured output completed (fixed json): {type(result).__name__}")
                            return result
                        else:
                            raise LLMProviderError("No JSON structure found in response")

                    except Exception as fix_error:
                        logger.error(f"JSON fix attempt failed: {fix_error}")
                        logger.error(f"Response text: {response.text[:500]}...")
                        raise LLMProviderError(f"Failed to parse JSON response: {je}")
            else:
                logger.error(f"Empty response from Gemini API")
                raise LLMProviderError("Empty response from Gemini API")

        except Exception as e:
            logger.error(f"Gemini structured output failed: {e}")
            raise LLMProviderError(f"Structured output failed: {e}")

    def _fix_json_format(self, json_text: str) -> str:
        """修复常见的JSON格式问题"""
        import re

        # 移除可能的markdown代码块标记
        json_text = re.sub(r'^```json\s*', '', json_text, flags=re.MULTILINE)
        json_text = re.sub(r'\s*```$', '', json_text, flags=re.MULTILINE)

        # 移除可能的注释
        json_text = re.sub(r'//.*$', '', json_text, flags=re.MULTILINE)

        # 修复截断的JSON - 找到最后一个完整的对象
        json_text = json_text.strip()

        # 如果JSON被截断，尝试找到最后一个完整的结构
        if not json_text.endswith(('}', ']')):
            # 找到最后一个完整的字段
            lines = json_text.split('\n')
            fixed_lines = []
            brace_count = 0

            for line in lines:
                # 计算大括号平衡
                brace_count += line.count('{') - line.count('}')

                # 如果这一行看起来是完整的字段，保留它
                if ('"' in line and ':' in line) or line.strip() in ['{', '}', '[', ']']:
                    fixed_lines.append(line)
                elif line.strip().endswith(','):
                    # 移除最后的逗号
                    fixed_lines.append(line.rstrip(','))
                    break
                elif brace_count < 0:
                    # 大括号不平衡，停止
                    break

            # 重新构建JSON
            json_text = '\n'.join(fixed_lines)

            # 确保大括号平衡
            open_braces = json_text.count('{')
            close_braces = json_text.count('}')
            if open_braces > close_braces:
                json_text += '\n' + '}' * (open_braces - close_braces)

        return json_text




    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        return {
            'provider': 'gemini',
            'model_name': self.model_name,
            'api_key_configured': bool(self.api_key),
            'supports_streaming': True,
            'supports_function_calling': True,
            'supports_structured_output': True,
            'config': self.config
        }
    
    async def health_check(self) -> bool:
        """健康检查"""
        try:
            if not self._client:
                return False

            # 简单的测试调用 - 使用同步API
            response = self._client.models.generate_content(
                model=self.model_name,
                contents="Hello"
            )
            return bool(response.text)

        except Exception as e:
            logger.warning(f"Gemini health check failed: {e}")
            return False
    
    async def check_availability(self) -> bool:
        """检查提供商可用性"""
        try:
            return GEMINI_AVAILABLE and bool(self.api_key)
        except Exception:
            return False
    
    async def estimate_cost(self, prompt: str, **kwargs) -> float:
        """估算成本"""
        # 简单的成本估算，基于token数量
        # Gemini的定价需要根据实际API文档调整
        estimated_tokens = len(prompt.split()) * 1.3  # 粗略估算
        cost_per_1k_tokens = 0.001  # 示例价格，需要根据实际调整
        return (estimated_tokens / 1000) * cost_per_1k_tokens
