from typing import List, Dict, Any, AsyncGenerator
import json
import re
from app.config import settings
from app.models import StoryStyle
from app.schemas.ai_outputs import (
    CharacterProfileOutput, ChapterChoicesOutput, ChapterOutput, ChapterSummaryOutput
)
from app.utils.logger import get_logger
from app.services.llm.providers import LLMProviderFactory
from app.services.llm.base import LLMProviderError, StreamChunk
from app.services.llm.models.pure_worldview_models import PureWorldView
from app.prompts.unified_manager import unified_prompt_manager

logger = get_logger(__name__)

class AIService:
    def __init__(self, default_provider: str = None):
        """初始化AI服务，使用新的LLM抽象层"""
        try:
            # 使用指定的默认provider或系统默认
            if default_provider:
                self.llm_provider = LLMProviderFactory.create_provider(default_provider)
                self.default_provider = default_provider
            else:
                self.llm_provider = LLMProviderFactory.create_default_provider()
                self.default_provider = None
            logger.info(f"AI服务初始化成功，使用Provider: {self.llm_provider.provider_type.value}")
        except Exception as e:
            logger.error(f"AI服务初始化失败: {e}")
            self.llm_provider = None
            self.default_provider = None

    def _get_provider(self, provider: str = None):
        """根据provider参数获取LLM Provider"""
        # 优先使用传入的provider，然后是default_provider，最后是当前provider
        target_provider = provider or self.default_provider
        if target_provider:
            try:
                from app.services.llm.base import LLMProviderType

                # 映射provider字符串到枚举
                provider_map = {
                    'gemini': LLMProviderType.GEMINI,
                    'siliconflow': LLMProviderType.SILICONFLOW
                }

                if target_provider.lower() in provider_map:
                    provider_type = provider_map[target_provider.lower()]
                    return LLMProviderFactory.create_provider(provider_type)
                else:
                    logger.warning(f"未知的provider: {target_provider}，使用默认provider")
                    return self.llm_provider
            except Exception as e:
                logger.error(f"创建provider失败: {e}，使用默认provider")
                return self.llm_provider
        else:
            # 使用默认provider
            return self.llm_provider

    def _extract_chapter_title_and_content(self, content: str) -> tuple[str, str]:
        """提取章节标题和内容"""
        import re

        content = content.strip()

        # 查找章节标题标记
        title_match = re.match(r'^CHAPTER_TITLE:\s*(.+?)(?:\n|$)', content, re.MULTILINE)

        if title_match:
            # 提取标题
            chapter_title = title_match.group(1).strip()
            # 移除标题行，获取正文
            content_without_title = re.sub(r'^CHAPTER_TITLE:\s*.+?(?:\n|$)', '', content, flags=re.MULTILINE).strip()
        else:
            # 如果没有找到标题标记，使用默认标题
            chapter_title = None
            content_without_title = content

        # 清理内容中可能残留的格式化标记
        cleaned_content = self._clean_chapter_content(content_without_title)

        return chapter_title, cleaned_content

    def _clean_chapter_content(self, content: str) -> str:
        """清理章节内容，移除格式化标记"""
        import re

        # 移除章节标题标记（如：**第X章：标题**、第X章、**第X章**等）
        patterns = [
            r'^\*\*第\d+章[：:][^*]*\*\*\s*',  # **第X章：标题**
            r'^\*\*第\d+章\*\*\s*',           # **第X章**
            r'^第\d+章[：:][^\n]*\n',         # 第X章：标题
            r'^第\d+章\s*\n',                 # 第X章
            r'^\s*第\d+章\s*',                # 开头的第X章
        ]

        cleaned_content = content.strip()
        for pattern in patterns:
            cleaned_content = re.sub(pattern, '', cleaned_content, flags=re.MULTILINE)

        return cleaned_content.strip()


    
    def _build_context(self, story_data: Dict[str, Any], worldview_context: str = None, previous_choice: str = None) -> str:
        """构建故事上下文"""
        context = f"故事风格：{story_data.get('style', '')}\n"
        context += f"故事标题：{story_data.get('title', '')}\n"
        context += f"当前章节：第{story_data.get('current_chapter_number', 1)}章\n"

        # 添加世界观框架
        if worldview_context:
            context += f"\n世界观框架：\n{worldview_context}\n"

        # 添加章节摘要历史
        if story_data.get('chapter_summaries'):
            summaries = story_data['chapter_summaries']
            logger.info(f"📚 使用历史章节摘要生成新章节，共 {len(summaries)} 个章节")

            context += "\n之前的故事情节：\n"
            used_chapters = []

            for summary_data in summaries:
                if isinstance(summary_data, dict):
                    chapter_num = summary_data.get('chapter_number', 0)
                    title = summary_data.get('title', '')
                    summary = summary_data.get('summary', '')
                    choices = summary_data.get('choices', [])

                    context += f"第{chapter_num}章 {title}：{summary}\n"
                    if choices:
                        context += f"  选择选项：{', '.join(choices)}\n"

                    used_chapters.append(f"第{chapter_num}章《{title}》")
                else:
                    # 兼容旧格式
                    context += f"章节摘要：{summary_data}\n"
                    used_chapters.append("历史章节")

            logger.info(f"📖 具体使用的章节摘要: {' | '.join(used_chapters)}")
        else:
            logger.info("📝 当前为第一章，无历史摘要可用")

        # 添加角色信息
        if story_data.get('character_info'):
            context += "\n主要角色：\n"
            for name, info in story_data['character_info'].items():
                context += f"{name}：{info}\n"

        # 添加用户选择（优先使用story_data中的user_choice）
        user_choice = story_data.get('user_choice') or previous_choice
        if user_choice:
            context += f"\n用户在上一章的选择：{user_choice}\n"
            context += "请根据这个选择继续故事发展，确保情节的连贯性和逻辑性。\n"

        return context
    
    # ❌ 已移除：generate_worldview 旧方法
    #
    # 原因：
    # 1. 需要用户提供主题，增加使用复杂度
    # 2. 功能与 generate_worldview_auto 重复
    # 3. 不符合智能化的产品方向
    # 4. 简化API，统一使用自动生成方法
    #
    # 替代方案：统一使用 generate_worldview_auto



    async def generate_worldview_auto(self, story_title: str, story_style: StoryStyle, provider: str = None) -> Dict[str, Any]:
        """智能生成世界观：AI根据标题和风格自动创建完整世界观"""
        logger.info(f"开始智能生成世界观 - 标题: {story_title}, 风格: {story_style.value}, Provider: {provider}")

        # 根据provider参数选择LLM Provider
        llm_provider = self._get_provider(provider)
        if not llm_provider:
            logger.error("LLM Provider未配置或配置错误")
            raise Exception("LLM Provider未配置或配置错误，无法生成世界观")

        # 设置世界观生成的中高创作性Temperature
        from app.config import settings
        llm_provider.config['temperature'] = settings.worldview_temperature
        logger.info(f"🌍 世界观生成使用中高创作性配置 - Temperature: {settings.worldview_temperature}")

        try:
            # 使用统一提示词管理器生成世界观提示词
            prompt = unified_prompt_manager.get_worldview_prompt(story_style, story_title)

            logger.info("使用提示词管理器生成世界观")
            response_text = await llm_provider.generate_text(prompt)

            # 检查响应是否为空
            if not response_text or len(response_text.strip()) == 0:
                logger.error("LLM返回空响应")
                raise Exception("AI生成世界观失败：返回空响应")

            logger.info(f"LLM返回响应长度: {len(response_text)} 字符")
            logger.info(f"LLM响应内容预览: {response_text[:200]}...")

            result = self._parse_worldview_response(response_text)

            # 验证解析结果
            if not result or len(result) == 0:
                logger.error("世界观解析失败，结果为空")
                raise Exception("AI生成世界观失败：解析结果为空")

            return result

        except Exception as e:
            logger.error(f"AI智能生成世界观失败: {e}")
            raise Exception(f"LLM API调用失败: {str(e)}")

    def _parse_worldview_response(self, response_text: str) -> Dict[str, Any]:
        """解析纯世界观响应文本"""
        try:
            logger.info("开始解析世界观响应")

            # 尝试提取JSON
            json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
            if json_match:
                try:
                    result = json.loads(json_match.group())
                    logger.info(f"成功解析JSON，包含 {len(result)} 个字段")
                    return result
                except json.JSONDecodeError as e:
                    logger.error(f"JSON解析失败: {e}")
                    logger.error(f"尝试解析的JSON: {json_match.group()[:500]}...")

            # 如果无法解析JSON，尝试从文本中提取信息
            logger.warning("无法解析JSON，尝试从文本中提取信息")

            # 简单的文本解析
            lines = response_text.strip().split('\n')
            extracted_info = {}

            for line in lines:
                line = line.strip()
                if ':' in line and len(line) > 10:
                    key, value = line.split(':', 1)
                    extracted_info[key.strip()] = value.strip()

            if len(extracted_info) > 0:
                logger.info(f"从文本中提取了 {len(extracted_info)} 个字段")
                return {"extracted_content": extracted_info, "raw_text": response_text}

            # 如果完全无法解析，抛出错误
            logger.error("完全无法解析世界观响应")
            logger.error(f"原始响应: {response_text}")
            raise Exception(f"无法解析世界观响应，原始内容: {response_text[:200]}...")

        except Exception as e:
            logger.error(f"解析世界观响应失败: {e}")
            raise Exception(f"世界观解析失败: {str(e)}")
    

    async def generate_chapter_stream(self, story_data: Dict[str, Any], worldview_context: str = None, previous_choice: str = None, provider: str = None, user_choice: str = None):
        """流式生成章节内容"""
        logger.info(f"开始流式生成章节 - 故事: {story_data.get('title', 'Unknown')}, 章节: {story_data.get('current_chapter_number', 1)}, Provider: {provider}, 用户选择: {user_choice}")

        # 根据provider参数选择LLM Provider
        llm_provider = self._get_provider(provider)
        if not llm_provider:
            logger.error("LLM Provider未配置或配置错误")
            raise Exception("LLM Provider未配置或配置错误，无法生成内容")

        # 设置章节生成的高创作性Temperature
        from app.config import settings
        llm_provider.config['temperature'] = settings.chapter_temperature
        logger.info(f"🎨 章节生成使用高创作性配置 - Temperature: {settings.chapter_temperature}")
        
        try:
            # 处理style字段，可能是枚举值或字符串
            style_value = story_data['style']
            if isinstance(style_value, StoryStyle):
                style = style_value
            else:
                style = StoryStyle(style_value)

            # 使用统一提示词管理器构建上下文和提示词
            context = self._build_context(story_data, worldview_context, previous_choice)

            # 获取角色档案信息
            character_info = story_data.get('character_profile_text', '')

            prompt = unified_prompt_manager.get_chapter_prompt(
                style=style,
                context=context,
                character_info=character_info,  # 传递角色档案信息
                min_words=settings.min_chapter_length,
                max_words=settings.max_chapter_length
            )
            
            # 使用新的LLM抽象层进行流式生成
            accumulated_content = ""
            chapter_number = story_data.get('current_chapter_number', 1)
            default_title = f"第{chapter_number}章"
            extracted_title = None
            title_sent = False

            try:
                # 使用流式生成
                async for chunk in llm_provider.generate_stream(
                    prompt,
                    show_thinking=settings.show_thinking_process
                ):
                    # 处理推理过程输出
                    if chunk.type == "thinking":
                        # 如果启用了thinking显示，转发给用户
                        if settings.show_thinking_process:
                            yield StreamChunk(
                                type="thinking",
                                content=chunk.content,
                                metadata={
                                    'provider': provider or 'default',
                                    'is_reasoning': True
                                }
                            )
                        continue  # thinking内容不参与正常的内容累积

                    # 安全处理chunk内容
                    chunk_content = chunk.content if chunk.content is not None else ""

                    if chunk_content:  # 只有非空内容才处理
                        accumulated_content += chunk_content

                        # 首次发送标题
                        if not title_sent:
                            # 尝试从累积内容中提取标题
                            if "CHAPTER_TITLE:" in accumulated_content:  # 检查是否包含标题标记
                                temp_title, _ = self._extract_chapter_title_and_content(accumulated_content)
                                if temp_title:
                                    extracted_title = temp_title

                            # 发送标题
                            final_title = extracted_title if extracted_title else default_title
                            yield StreamChunk(
                                type="title",
                                content=final_title,
                                metadata={'provider': provider or 'default'}
                            )
                            title_sent = True

                        # 清理内容，移除标题标记
                        cleaned_chunk = chunk_content
                        if "CHAPTER_TITLE:" in chunk_content:
                            _, cleaned_chunk = self._extract_chapter_title_and_content(chunk_content)

                        # 发送清理后的内容块
                        if cleaned_chunk.strip():  # 只发送非空内容
                            yield StreamChunk(
                                type="content",
                                content=cleaned_chunk,
                                metadata={'provider': provider or 'default'}
                            )

                    # 检查是否完成
                    if (hasattr(chunk, 'is_final') and chunk.is_final) or \
                       (hasattr(chunk, 'metadata') and chunk.metadata and chunk.metadata.get('is_final')) or \
                       (hasattr(chunk, 'type') and chunk.type == "complete"):
                        break

                # 提取标题和清理内容
                extracted_title, final_content = self._extract_chapter_title_and_content(accumulated_content.strip())

                # 确定最终标题
                if extracted_title and not title_sent:
                    # 如果还没发送标题且提取到了标题，使用提取的标题
                    final_title = extracted_title
                elif extracted_title:
                    # 如果已经发送了标题，更新最终标题用于保存
                    final_title = extracted_title
                else:
                    # 使用默认标题
                    final_title = default_title
                # 调整字数要求，增加容错性
                min_length = 1200  # 降低最低要求
                if not final_content or len(final_content) < min_length:
                    logger.error(f"生成的章节内容过短：{len(final_content)} 字符")
                    logger.error(f"内容预览：{final_content[:200]}")
                    raise Exception(f"章节内容生成失败：内容过短（{len(final_content)}字符），要求至少{min_length}字符")
                elif len(final_content) < 1500:
                    logger.warning(f"章节内容略短：{len(final_content)} 字符，建议长度1500+字符，但仍可接受")

                # 检查内容是否被截断（结尾不完整）
                if not final_content.endswith(('，', '。', '！', '？', '"', '"', '…', '……')):
                    logger.warning(f"章节内容可能被截断，结尾字符：'{final_content[-20:]}'")
                    # 如果内容太短且被截断，抛出错误
                    if len(final_content) < 2500:
                        raise Exception(f"章节内容被截断且过短（{len(final_content)}字符），需要重新生成")
                    else:
                        # 内容足够长但被截断，尝试添加合适的结尾
                        logger.info("内容足够长但被截断，尝试修复结尾")
                        if final_content.endswith(('的', '了', '着', '过', '在', '是', '有', '也', '都', '就', '要', '会', '能', '可', '不')):
                            final_content += "。"
                        elif final_content.endswith(('，', '、')):
                            final_content = final_content[:-1] + "。"
                        else:
                            final_content += "……"
                        accumulated_content = final_content

                # 发送内容完成信号（但还需要生成选择选项）
                yield StreamChunk(
                    type="content_complete",
                    content="",
                    metadata={
                        'title': final_title,
                        'content': final_content,
                        'provider': provider or 'default'
                    }
                )

                # 生成选择选项（如果需要）
                if story_data.get('generate_choices', True):
                    logger.info("开始生成选择选项...")
                    try:
                        choices = await self.generate_choices(
                            accumulated_content.strip(),
                            StoryStyle(story_data['style'])
                        )
                        logger.info(f"选择选项生成成功: {choices}")

                        # 发送最终完成信号，包含选择选项
                        yield StreamChunk(
                            type="complete",
                            content="",
                            metadata={
                                'title': final_title,
                                'content': final_content,
                                'choices': choices,
                                'provider': provider or 'default'
                            }
                        )
                        logger.info("章节完成信号已发送")
                    except Exception as e:
                        logger.error(f"生成选择选项失败: {e}")
                        # 提供备用选择选项，确保章节能够保存
                        backup_choices = [
                            "继续探索前方的道路",
                            "停下来仔细观察周围环境",
                            "寻找其他可能的路径"
                        ]
                        logger.info(f"使用备用选择选项: {backup_choices}")

                        # 即使选择选项生成失败，也要发送完成信号
                        yield StreamChunk(
                            type="complete",
                            content="",
                            metadata={
                                'title': final_title,
                                'content': final_content,
                                'choices': backup_choices,
                                'provider': provider or 'default'
                            }
                        )
                        logger.info("章节完成信号已发送（使用备用选择）")
                else:
                    # 如果不需要生成选择选项，直接发送完成信号
                    yield StreamChunk(
                        type="complete",
                        content="",
                        metadata={
                            'title': final_title,
                            'content': final_content,
                            'choices': [],
                            'provider': provider or 'default'
                        }
                    )
                    logger.info("章节完成信号已发送（无选择选项）")

            except Exception as e:
                logger.error(f"流式生成失败: {e}")

                # 检查是否有部分内容可以保存
                if accumulated_content and len(accumulated_content.strip()) > 1000:
                    logger.warning("流式生成失败，但有部分内容可以保存，尝试保存部分内容")

                    # 提取标题和内容
                    extracted_title, final_content = self._extract_chapter_title_and_content(accumulated_content.strip())

                    # 确定标题
                    if extracted_title:
                        final_title = extracted_title
                    else:
                        final_title = default_title

                    # 确保内容以合适的标点结尾
                    if final_content and not final_content.endswith(('，', '。', '！', '？', '"', '"', '…', '……')):
                        if final_content.endswith(('的', '了', '着', '过', '在', '是', '有', '也', '都', '就', '要', '会', '能', '可', '不')):
                            final_content += "。"
                        else:
                            final_content += "……"

                    # 发送内容完成信号
                    yield {
                        "type": "content_complete",
                        "title": final_title,
                        "content": final_content
                    }

                    # 提供备用选择选项并发送完成信号
                    backup_choices = [
                        "继续探索前方的道路",
                        "停下来仔细观察周围环境",
                        "寻找其他可能的路径"
                    ]

                    yield {
                        "type": "chapter_complete",
                        "data": {
                            "title": final_title,
                            "content": final_content,
                            "choices": backup_choices
                        }
                    }
                    logger.info("流式生成失败但已保存部分内容")
                else:
                    # 如果没有足够的内容，抛出错误
                    raise Exception(f"章节流式生成失败且内容不足: {str(e)}")

        except Exception as e:
            logger.error(f"AI流式生成章节失败: {e}")
            yield {
                "type": "error",
                "message": f"LLM API调用失败: {str(e)}"
            }
    
    async def generate_chapter_summary(self, chapter_content: str, provider: str = None) -> str:
        """生成章节摘要"""
        logger.info(f"开始生成章节摘要 - Provider: {provider}")

        # 根据provider参数选择LLM Provider
        llm_provider = self._get_provider(provider)
        if not llm_provider:
            logger.error("LLM Provider未配置或配置错误")
            raise Exception("LLM Provider未配置或配置错误，无法生成摘要")

        # 设置摘要生成的低创作性Temperature（保持准确性）
        from app.config import settings
        llm_provider.config['temperature'] = settings.summary_temperature
        logger.info(f"📝 摘要生成使用低创作性配置 - Temperature: {settings.summary_temperature}")

        try:
            # 使用统一提示词管理器生成摘要提示词
            prompt = unified_prompt_manager.get_summary_prompt(chapter_content)

            # 生成摘要（为推理模型设置合适的参数）
            summary = await llm_provider.generate_text(
                prompt,
                max_tokens=500,  # 限制摘要长度
                temperature=settings.summary_temperature
            )

            # 清理摘要文本
            summary = summary.strip()
            if summary.startswith('"') and summary.endswith('"'):
                summary = summary[1:-1]

            logger.info(f"章节摘要生成完成: {summary[:50]}...")
            return summary

        except Exception as e:
            logger.error(f"生成章节摘要失败: {e}")
            raise Exception(f"生成章节摘要失败: {str(e)}")

    async def generate_choices(self, chapter_content: str, story_style: StoryStyle, provider: str = None) -> List[str]:
        """生成选择选项"""
        logger.info(f"开始生成选择选项 - 风格: {story_style.value}, Provider: {provider}")

        # 根据provider参数选择LLM Provider
        llm_provider = self._get_provider(provider)
        if not llm_provider:
            logger.error("LLM Provider未配置或配置错误")
            raise Exception("LLM Provider未配置或配置错误，无法生成选择")

        # 设置选择选项生成的中等创作性Temperature
        from app.config import settings
        llm_provider.config['temperature'] = settings.choices_temperature
        logger.info(f"🎯 选择选项生成使用中等创作性配置 - Temperature: {settings.choices_temperature}")
        
        try:
            # 使用统一提示词管理器生成选择提示词
            prompt = unified_prompt_manager.get_choices_prompt(chapter_content, story_style)

            # 使用新的LLM抽象层生成选择
            try:
                response_text = await llm_provider.generate_text(prompt)
                logger.info(f"AI返回的原始选择内容: {response_text}")

                # 尝试解析JSON数组 - 改进正则表达式支持多行
                json_match = re.search(r'\[[\s\S]*?\]', response_text)
                logger.info(f"正则匹配结果: {json_match.group() if json_match else 'None'}")

                if json_match:
                    try:
                        choices = json.loads(json_match.group())
                        if isinstance(choices, list) and len(choices) >= 3:
                            logger.info(f"成功解析选择选项: {choices}")
                            return choices[:3]
                    except json.JSONDecodeError as e:
                        logger.error(f"JSON解析错误: {e}")

                # 备用解析方法：尝试从文本中提取选择
                logger.warning("JSON解析失败，尝试备用解析方法")
                fallback_choices = self._extract_choices_fallback(response_text)
                if fallback_choices and len(fallback_choices) >= 3:
                    logger.info(f"备用方法成功提取选择: {fallback_choices}")
                    return fallback_choices[:3]

                # 如果所有方法都失败，抛出错误
                logger.error("无法解析选择选项的JSON格式")
                logger.error(f"AI原始响应: {response_text}")
                raise Exception("选择选项解析失败：无法从AI响应中提取有效的选择选项")

            except Exception as e:
                logger.error(f"生成选择失败: {e}")
                raise Exception(f"选择选项生成失败: {str(e)}")

        except Exception as e:
            logger.error(f"AI生成选择失败: {e}")
            raise Exception(f"LLM API调用失败: {str(e)}")





    def _extract_choices_fallback(self, response_text: str) -> List[str]:
        """备用选择提取方法"""
        try:
            # 方法1: 查找引号包围的文本
            import re

            # 查找所有引号包围的文本
            quoted_texts = re.findall(r'"([^"]{10,50})"', response_text)
            if len(quoted_texts) >= 3:
                return quoted_texts[:3]

            # 方法2: 按行分割，查找选择格式
            lines = response_text.split('\n')
            choices = []

            for line in lines:
                line = line.strip()
                # 查找类似 "1. xxx" 或 "- xxx" 或 "• xxx" 的格式
                if re.match(r'^[\d\-•]\s*[\.、]\s*(.+)', line):
                    choice = re.sub(r'^[\d\-•]\s*[\.、]\s*', '', line).strip()
                    if 5 <= len(choice) <= 50:  # 合理的选择长度
                        choices.append(choice)
                # 或者直接是引号包围的文本
                elif line.startswith('"') and line.endswith('"'):
                    choice = line.strip('"')
                    if 5 <= len(choice) <= 50:
                        choices.append(choice)

            if len(choices) >= 3:
                return choices[:3]

            # 方法3: 生成默认选择
            logger.warning("无法从AI响应中提取选择，使用默认选择")
            return [
                "继续前进，探索未知",
                "谨慎观察，寻找线索",
                "暂时退后，重新考虑"
            ]

        except Exception as e:
            logger.error(f"备用选择提取失败: {e}")
            return [
                "继续前进，探索未知",
                "谨慎观察，寻找线索",
                "暂时退后，重新考虑"
            ]

    async def generate_character_profile(self, prompt: str, provider: str = None) -> str:
        """生成角色档案"""
        logger.info(f"开始生成角色档案 - Provider: {provider}")

        # 根据provider参数选择LLM Provider
        llm_provider = self._get_provider(provider)
        if not llm_provider:
            logger.error("LLM Provider未配置或配置错误")
            raise Exception("LLM Provider未配置或配置错误，无法生成角色档案")

        # 设置角色档案生成的中等创作性Temperature
        from app.config import settings
        llm_provider.config['temperature'] = settings.worldview_temperature
        logger.info(f"🎭 角色档案生成使用中等创作性配置 - Temperature: {settings.worldview_temperature}")

        try:
            # 生成角色档案
            character_profile = await llm_provider.generate_text(
                prompt,
                max_tokens=1000,  # 角色档案需要较多内容
                temperature=settings.worldview_temperature
            )

            # 清理角色档案文本
            character_profile = character_profile.strip()

            logger.info(f"角色档案生成完成: {character_profile[:100]}...")
            return character_profile

        except Exception as e:
            logger.error(f"生成角色档案失败: {e}")
            raise Exception(f"生成角色档案失败: {str(e)}")

    async def generate_character_profile_structured(
        self,
        worldview_data: Dict[str, Any],
        story_style: str,
        provider: str = None
    ) -> CharacterProfileOutput:
        """使用结构化输出生成角色档案"""
        logger.info(f"🎭 开始结构化生成角色档案 - Provider: {provider}")

        # 根据provider参数选择LLM Provider
        llm_provider = self._get_provider(provider)
        if not llm_provider:
            logger.error("LLM Provider未配置或配置错误")
            raise Exception("LLM Provider未配置或配置错误，无法生成角色档案")

        # 设置角色档案生成的中等创作性Temperature
        from app.config import settings
        llm_provider.config['temperature'] = settings.worldview_temperature
        logger.info(f"🎭 角色档案生成使用中等创作性配置 - Temperature: {settings.worldview_temperature}")

        # 构建简洁的提示词（不需要格式要求）
        prompt = f"""
基于以下世界观信息，为主角创建详细的角色档案：

世界观信息：
世界设定：{worldview_data.get('world_setting', '')}
力量体系：{worldview_data.get('power_system', '')}
主角描述：{worldview_data.get('main_character', '')}
故事风格：{story_style}
故事主题：{worldview_data.get('story_themes', '')}

角色设计要求：
1. 姓名要求：必须是具体的中文姓名，不能使用"主角"、"我"等代称，符合{story_style}风格
2. 性格设计：要有明显的优点和缺点，避免完美人设，性格特点要能产生戏剧冲突
3. 背景设定：必须与世界观设定完全吻合，要有足够的深度支撑长篇故事
4. 说话方式：要有明显的个人特色，符合角色的出身和性格
5. 成长目标：要能支撑整个故事的发展，与世界观的核心冲突相关

请创建一个立体、有深度、符合世界观的角色档案。
"""

        try:
            # 使用结构化输出生成角色档案
            character_profile = await llm_provider.generate_structured_output(
                prompt=prompt,
                output_model=CharacterProfileOutput
            )

            logger.info(f"✅ 结构化角色档案生成完成: {character_profile.name}")
            return character_profile

        except Exception as e:
            logger.error(f"结构化生成角色档案失败: {e}")
            raise Exception(f"结构化生成角色档案失败: {str(e)}")

    async def generate_chapter_choices_structured(
        self,
        chapter_content: str,
        story_context: str,
        provider: str = None
    ) -> ChapterChoicesOutput:
        """使用结构化输出生成章节选项"""
        logger.info(f"🎲 开始结构化生成章节选项 - Provider: {provider}")

        # 根据provider参数选择LLM Provider
        llm_provider = self._get_provider(provider)
        if not llm_provider:
            logger.error("LLM Provider未配置或配置错误")
            raise Exception("LLM Provider未配置或配置错误，无法生成选项")

        # 构建选项生成提示词
        prompt = f"""
基于以下章节内容和故事背景，生成3个高质量的选择选项：

故事背景：
{story_context}

章节内容：
{chapter_content}

选项要求：
1. 每个选项都要有明确的行动方向
2. 选项之间要有明显的差异和对比
3. 要符合当前情境和角色性格
4. 每个选项控制在15字以内
5. 要能推动故事发展

请生成3个不同风格的选择选项。
"""

        try:
            # 使用结构化输出生成选项
            choices_output = await llm_provider.generate_structured_output(
                prompt=prompt,
                output_model=ChapterChoicesOutput
            )

            logger.info(f"✅ 结构化章节选项生成完成: {len(choices_output.choices)} 个选项")
            return choices_output

        except Exception as e:
            logger.error(f"结构化生成章节选项失败: {e}")
            raise Exception(f"结构化生成章节选项失败: {str(e)}")

    async def generate_choices_structured(
        self,
        chapter_content: str,
        story_style: StoryStyle,
        provider: str = None
    ) -> ChapterChoicesOutput:
        """使用结构化输出生成章节选项（替代传统解析方法）"""
        logger.info(f"🎲 开始结构化生成章节选项 - 风格: {story_style.value}, Provider: {provider}")

        # 根据provider参数选择LLM Provider
        llm_provider = self._get_provider(provider)
        if not llm_provider:
            logger.error("LLM Provider未配置或配置错误")
            raise Exception("LLM Provider未配置或配置错误，无法生成选项")

        # 设置选择选项生成的中等创作性Temperature
        from app.config import settings
        llm_provider.config['temperature'] = settings.choices_temperature
        logger.info(f"🎯 选择选项生成使用中等创作性配置 - Temperature: {settings.choices_temperature}")

        # 构建简洁的选项生成提示词（无需格式要求）
        prompt = f"""
基于以下章节内容，生成3个高质量的选择选项：

章节内容：
{chapter_content}

故事风格：{story_style.value}

选项要求：
1. 每个选项都要有明确的行动方向
2. 选项之间要有明显的差异和对比
3. 要符合当前情境和角色性格
4. 每个选项控制在5-30字之间
5. 要能推动故事发展
6. 符合{story_style.value}风格的特点

请生成3个不同风格的选择选项。
"""

        try:
            # 使用结构化输出生成选项
            choices_output = await llm_provider.generate_structured_output(
                prompt=prompt,
                output_model=ChapterChoicesOutput
            )

            logger.info(f"✅ 结构化章节选项生成完成: {len(choices_output.choices)} 个选项")
            logger.info(f"生成的选项: {choices_output.choices}")
            return choices_output

        except Exception as e:
            logger.error(f"结构化生成章节选项失败: {e}")
            raise Exception(f"结构化生成章节选项失败: {str(e)}")

    async def generate_chapter_content_structured(
        self,
        story_context: Dict[str, Any],
        provider: str = None
    ) -> ChapterOutput:
        """使用结构化输出生成章节内容（TDD Green阶段实现）"""
        logger.info(f"🎯 开始结构化生成章节内容 - Provider: {provider}")

        # 根据provider参数选择LLM Provider
        llm_provider = self._get_provider(provider)
        if not llm_provider:
            logger.error("LLM Provider未配置或配置错误")
            raise Exception("LLM Provider未配置或配置错误，无法生成章节内容")

        # 设置章节生成的高创作性Temperature
        from app.config import settings
        # 为了生成更长更丰富的内容，使用稍高的温度
        enhanced_temperature = min(settings.chapter_temperature + 0.1, 0.9)
        llm_provider.config['temperature'] = enhanced_temperature
        logger.info(f"📖 章节内容生成使用增强创作性配置 - Temperature: {enhanced_temperature}")

        # 🎯 使用优化的章节生成提示词模板
        character_name = story_context.get('character_profile', {}).get('name', '主角')
        character_traits = story_context.get('character_profile', {}).get('personality_traits', '')
        speaking_style = story_context.get('character_profile', {}).get('speaking_style', '')
        current_state = story_context.get('character_profile', {}).get('current_state', '')
        user_choice = story_context.get('user_choice', '')
        story_style = story_context.get('style', StoryStyle.XIANXIA).value

        # 构建上下文信息
        context = f"""
**故事背景：**
- 标题：{story_context.get('title', '')}
- 风格：{story_style}
- 世界观：{story_context.get('worldview', {})}

**角色信息：**
- 主角姓名：{character_name}
- 性格特点：{character_traits}
- 说话风格：{speaking_style}
- 当前状态：{current_state}

**用户选择：**
{user_choice}
"""

        # 使用文档中的优化提示词模板
        prompt = f"""
{story_style}风格小说章节生成

{context}

**🎭 角色一致性要求（必须严格执行）：**
- 主角姓名：{character_name}
- 必须使用第三人称叙述（"他"、"{character_name}"、"这位年轻人"等）
- 严禁使用第一人称（"我"、"我的"、"我们"等）
- 角色性格特点：{character_traits}
- 说话风格：{speaking_style}
- 角色行为必须符合既定性格，不能出现性格分裂

**📝 输出格式要求（必须严格遵守）：**
生成包含标题和内容的完整章节。

**📚 内容要求（必须遵守）：**
1. **字数要求**：章节内容必须在2000-5000字之间，这是硬性要求！
2. **内容质量**：必须是完整的故事章节，包含详细的情节描述、人物对话、环境描写
3. **角色塑造**：通过行动、对话、心理活动展现{character_name}的性格特点
4. **结构完整**：有明确的开头、发展、高潮、结尾
5. **悬念设置**：章节结尾要为下一个选择做铺垫，但要明确描述结尾状态
6. **标题要求**：章节标题要简洁有力，体现本章核心情节，不超过15个字

**🎨 写作技巧要求：**
1. **节奏控制**：合理安排情节节奏，张弛有度
2. **人物塑造**：通过行动、对话、心理活动展现人物性格
3. **环境描写**：适当的环境描写增强代入感，至少300字的环境描写
4. **冲突设置**：每章都要有明确的冲突或转折点
5. **情感渲染**：通过细节描写增强情感共鸣
6. **对话真实**：{character_name}的对话要符合其说话风格：{speaking_style}
7. **行为一致**：{character_name}的行为要体现其性格：{character_traits}
8. **内心独白**：包含至少500字的角色内心活动描写
9. **动作描写**：详细描写战斗、移动、互动等动作场面

**❌ 严格禁止的行为：**
- 使用第一人称叙述（"我"、"我的"等）
- 角色行为与既定性格不符
- 模糊的结尾描述
- 内容过短（少于2000字）

**重要提醒**：
- 请确保生成足够长的内容，至少2000字！通过详细的描写、对话、心理活动来达到字数要求
- 始终记住主角的名字是{character_name}，不是"我"
- 每个场景都要有充分的细节描写
- 对话要生动自然，符合角色性格
- 心理活动要深入细致

请基于以上要求创作高质量的章节内容。
"""

        try:
            # 🔄 使用增强的重试机制确保内容长度符合要求
            max_retries = 3
            for attempt in range(max_retries + 1):
                try:
                    logger.info(f"📝 尝试生成章节内容 (第{attempt + 1}次)")

                    # 根据重试次数调整提示词强度
                    current_prompt = prompt
                    if attempt > 0:
                        length_emphasis = f"""

**🚨 重要提醒（第{attempt + 1}次生成）**：
- 前{attempt}次生成的内容长度不足，请务必注意！
- 章节内容必须达到2000字以上，这是硬性要求！
- 请通过以下方式确保长度：
  1. 详细的环境描写（至少300字）
  2. 丰富的对话内容（至少500字）
  3. 深入的心理描写（至少400字）
  4. 生动的动作场面（至少400字）
  5. 细致的情节发展（至少400字）
- 不要急于结束章节，要充分展开每个场景
- 每个对话都要有充分的背景描写
- 每个动作都要有详细的过程描述

**字数分配建议**：
- 开头场景设置：300-400字
- 主要情节发展：800-1000字
- 对话和互动：400-600字
- 心理活动描写：300-400字
- 结尾和悬念：200-300字
- 总计：2000-2700字

请严格按照这个字数要求生成内容！"""
                        current_prompt += length_emphasis

                    # 使用结构化输出生成章节
                    chapter_output = await llm_provider.generate_structured_output(
                        prompt=current_prompt,
                        output_model=ChapterOutput
                    )

                    logger.info(f"✅ 结构化章节内容生成完成: {chapter_output.title}")
                    logger.info(f"章节长度: {len(chapter_output.content)} 字符")
                    return chapter_output

                except Exception as e:
                    if "String should have at least 2000 characters" in str(e) and attempt < max_retries:
                        logger.warning(f"⚠️  第{attempt + 1}次生成内容长度不足: {len(chapter_output.content) if 'chapter_output' in locals() else '未知'} 字符")
                        logger.warning(f"🔄 准备第{attempt + 2}次重试，将加强长度要求...")
                        continue
                    else:
                        raise e

        except Exception as e:
            # 🔄 如果常规方法失败，尝试分段生成策略
            logger.warning(f"常规生成失败，尝试分段生成策略: {e}")
            try:
                return await self._generate_chapter_by_segments(
                    story_context=story_context,
                    llm_provider=llm_provider
                )
            except Exception as fallback_error:
                logger.error(f"分段生成也失败: {fallback_error}")
                raise Exception(f"结构化生成章节内容失败: {str(e)}")

    async def _generate_chapter_by_segments(
        self,
        story_context: Dict[str, Any],
        llm_provider
    ) -> ChapterOutput:
        """分段生成策略：先生成大纲，再逐段扩展"""
        logger.info("🔄 使用分段生成策略")

        character_name = story_context.get('character_profile', {}).get('name', '主角')

        # 第一步：生成章节大纲
        outline_prompt = f"""
基于故事背景，生成一个详细的章节大纲：

{story_context}

请生成包含以下部分的章节大纲：
1. 章节标题（简洁有力，不超过15字）
2. 开头场景（300字左右）
3. 主要情节发展（800字左右）
4. 高潮部分（400字左右）
5. 结尾和悬念（300字左右）

每部分都要有具体的情节描述，确保总长度达到2000字以上。
主角姓名：{character_name}，必须使用第三人称。
"""

        # 生成完整内容（简化版，直接生成而不是分段）
        full_prompt = f"""
{outline_prompt}

请直接生成完整的章节内容，包含标题和正文。
正文必须达到2000字以上，包含详细的描写、对话、心理活动。
"""

        try:
            chapter_output = await llm_provider.generate_structured_output(
                prompt=full_prompt,
                output_model=ChapterOutput
            )

            logger.info(f"✅ 分段策略生成成功: {chapter_output.title}, 长度: {len(chapter_output.content)}")
            return chapter_output

        except Exception as e:
            logger.error(f"分段生成策略失败: {e}")
            raise e

    async def generate_chapter_summary_structured(
        self,
        chapter_content: str,
        provider: str = None
    ) -> ChapterSummaryOutput:
        """使用结构化输出生成章节摘要（TDD Green阶段实现）"""
        logger.info(f"📝 开始结构化生成章节摘要 - Provider: {provider}")

        # 根据provider参数选择LLM Provider
        llm_provider = self._get_provider(provider)
        if not llm_provider:
            logger.error("LLM Provider未配置或配置错误")
            raise Exception("LLM Provider未配置或配置错误，无法生成章节摘要")

        # 🎯 使用优化的摘要生成提示词
        prompt = f"""
请阅读以下完整章节内容，并生成详细的章节摘要：

章节内容：
{chapter_content}

**摘要格式要求（必须严格遵守）：**
摘要必须包含两个核心维度的信息：

**1. 主角信息维度：**
- 主角的精确位置（具体在哪里，不能模糊）
- 主角的身体状态（健康、疲劳、装备、伤势等）
- 主角的心理状态（情绪、想法、决心等）
- 主角当前拥有的能力/物品/工具
- 主角与其他角色的关系变化

**2. 故事发展维度：**
- 本章发生的主要事件和关键转折点
- 当前正在进行中的事件（未完成的动作或计划）
- 新发现的重要线索或信息
- 当前面临的主要冲突或挑战
- 环境或世界状态的重要变化

**具体要求：**
1. **字数控制**：摘要长度必须控制在200-400字之间，这是硬性要求！
2. **信息完整**：必须涵盖上述两个维度的所有要点
3. **精确描述**：避免使用"某处"、"不久"等模糊词汇
4. **衔接导向**：重点描述章节结尾状态，为下章提供明确起点
5. **自然语言**：使用流畅的自然语言，不要使用列表格式
6. **角色姓名**：必须使用角色的具体姓名，不能使用"主角"、"我"等代称

**重要提醒**：
- 摘要是下一章生成的重要依据，必须提供足够详细的状态信息
- 特别注意描述主角的确切位置和状态，避免下章出现位置跳跃
- 确保包含未完成的事件，让下章能够自然承接
- 必须使用主角的具体姓名，建立角色一致性
- 摘要长度必须达到200字以上，通过详细描述各个要点来确保长度

请严格按照以上要求生成高质量的章节摘要。
"""

        try:
            # 使用结构化输出生成摘要
            summary_output = await llm_provider.generate_structured_output(
                prompt=prompt,
                output_model=ChapterSummaryOutput
            )

            logger.info(f"✅ 结构化章节摘要生成完成")
            logger.info(f"摘要长度: {len(summary_output.summary)} 字符")
            return summary_output

        except Exception as e:
            logger.error(f"结构化生成章节摘要失败: {e}")
            raise Exception(f"结构化生成章节摘要失败: {str(e)}")


# 创建全局AI服务实例
ai_service = AIService()