from typing import List, Dict, Any, AsyncGenerator
import json
import re
from app.config import settings
from app.models import StoryStyle
from app.utils.logger import get_logger
from app.services.llm.providers import LLMProviderFactory
from app.services.llm.base import LLMProviderError, StreamChunk
from app.services.llm.models.pure_worldview_models import PureWorldView
from app.prompts.unified_manager import unified_prompt_manager

logger = get_logger(__name__)

class AIService:
    def __init__(self, default_provider: str = None):
        """初始化AI服务，使用新的LLM抽象层"""
        try:
            # 使用指定的默认provider或系统默认
            if default_provider:
                self.llm_provider = LLMProviderFactory.create_provider(default_provider)
                self.default_provider = default_provider
            else:
                self.llm_provider = LLMProviderFactory.create_default_provider()
                self.default_provider = None
            logger.info(f"AI服务初始化成功，使用Provider: {self.llm_provider.provider_type.value}")
        except Exception as e:
            logger.error(f"AI服务初始化失败: {e}")
            self.llm_provider = None
            self.default_provider = None

    def _get_provider(self, provider: str = None):
        """根据provider参数获取LLM Provider"""
        # 优先使用传入的provider，然后是default_provider，最后是当前provider
        target_provider = provider or self.default_provider
        if target_provider:
            try:
                from app.services.llm.base import LLMProviderType

                # 映射provider字符串到枚举
                provider_map = {
                    'gemini': LLMProviderType.GEMINI,
                    'siliconflow': LLMProviderType.SILICONFLOW
                }

                if target_provider.lower() in provider_map:
                    provider_type = provider_map[target_provider.lower()]
                    return LLMProviderFactory.create_provider(provider_type)
                else:
                    logger.warning(f"未知的provider: {target_provider}，使用默认provider")
                    return self.llm_provider
            except Exception as e:
                logger.error(f"创建provider失败: {e}，使用默认provider")
                return self.llm_provider
        else:
            # 使用默认provider
            return self.llm_provider

    def _extract_chapter_title_and_content(self, content: str) -> tuple[str, str]:
        """提取章节标题和内容"""
        import re

        content = content.strip()

        # 查找章节标题标记
        title_match = re.match(r'^CHAPTER_TITLE:\s*(.+?)(?:\n|$)', content, re.MULTILINE)

        if title_match:
            # 提取标题
            chapter_title = title_match.group(1).strip()
            # 移除标题行，获取正文
            content_without_title = re.sub(r'^CHAPTER_TITLE:\s*.+?(?:\n|$)', '', content, flags=re.MULTILINE).strip()
        else:
            # 如果没有找到标题标记，使用默认标题
            chapter_title = None
            content_without_title = content

        # 清理内容中可能残留的格式化标记
        cleaned_content = self._clean_chapter_content(content_without_title)

        return chapter_title, cleaned_content

    def _clean_chapter_content(self, content: str) -> str:
        """清理章节内容，移除格式化标记"""
        import re

        # 移除章节标题标记（如：**第X章：标题**、第X章、**第X章**等）
        patterns = [
            r'^\*\*第\d+章[：:][^*]*\*\*\s*',  # **第X章：标题**
            r'^\*\*第\d+章\*\*\s*',           # **第X章**
            r'^第\d+章[：:][^\n]*\n',         # 第X章：标题
            r'^第\d+章\s*\n',                 # 第X章
            r'^\s*第\d+章\s*',                # 开头的第X章
        ]

        cleaned_content = content.strip()
        for pattern in patterns:
            cleaned_content = re.sub(pattern, '', cleaned_content, flags=re.MULTILINE)

        return cleaned_content.strip()


    
    def _build_context(self, story_data: Dict[str, Any], worldview_context: str = None, previous_choice: str = None) -> str:
        """构建故事上下文"""
        context = f"故事风格：{story_data.get('style', '')}\n"
        context += f"故事标题：{story_data.get('title', '')}\n"
        context += f"当前章节：第{story_data.get('current_chapter_number', 1)}章\n"

        # 添加世界观框架
        if worldview_context:
            context += f"\n世界观框架：\n{worldview_context}\n"

        # 添加章节摘要历史
        if story_data.get('chapter_summaries'):
            summaries = story_data['chapter_summaries']
            logger.info(f"📚 使用历史章节摘要生成新章节，共 {len(summaries)} 个章节")

            context += "\n之前的故事情节：\n"
            used_chapters = []

            for summary_data in summaries:
                if isinstance(summary_data, dict):
                    chapter_num = summary_data.get('chapter_number', 0)
                    title = summary_data.get('title', '')
                    summary = summary_data.get('summary', '')
                    choices = summary_data.get('choices', [])

                    context += f"第{chapter_num}章 {title}：{summary}\n"
                    if choices:
                        context += f"  选择选项：{', '.join(choices)}\n"

                    used_chapters.append(f"第{chapter_num}章《{title}》")
                else:
                    # 兼容旧格式
                    context += f"章节摘要：{summary_data}\n"
                    used_chapters.append("历史章节")

            logger.info(f"📖 具体使用的章节摘要: {' | '.join(used_chapters)}")
        else:
            logger.info("📝 当前为第一章，无历史摘要可用")

        # 添加角色信息
        if story_data.get('character_info'):
            context += "\n主要角色：\n"
            for name, info in story_data['character_info'].items():
                context += f"{name}：{info}\n"

        # 添加用户选择（优先使用story_data中的user_choice）
        user_choice = story_data.get('user_choice') or previous_choice
        if user_choice:
            context += f"\n用户在上一章的选择：{user_choice}\n"
            context += "请根据这个选择继续故事发展，确保情节的连贯性和逻辑性。\n"

        return context
    
    # ❌ 已移除：generate_worldview 旧方法
    #
    # 原因：
    # 1. 需要用户提供主题，增加使用复杂度
    # 2. 功能与 generate_worldview_auto 重复
    # 3. 不符合智能化的产品方向
    # 4. 简化API，统一使用自动生成方法
    #
    # 替代方案：统一使用 generate_worldview_auto



    async def generate_worldview_auto(self, story_title: str, story_style: StoryStyle, provider: str = None) -> Dict[str, Any]:
        """智能生成世界观：AI根据标题和风格自动创建完整世界观"""
        logger.info(f"开始智能生成世界观 - 标题: {story_title}, 风格: {story_style.value}, Provider: {provider}")

        # 根据provider参数选择LLM Provider
        llm_provider = self._get_provider(provider)
        if not llm_provider:
            logger.error("LLM Provider未配置或配置错误")
            raise Exception("LLM Provider未配置或配置错误，无法生成世界观")

        # 设置世界观生成的中高创作性Temperature
        from app.config import settings
        llm_provider.config['temperature'] = settings.worldview_temperature
        logger.info(f"🌍 世界观生成使用中高创作性配置 - Temperature: {settings.worldview_temperature}")

        try:
            # 使用统一提示词管理器生成世界观提示词
            prompt = unified_prompt_manager.get_worldview_prompt(story_style, story_title)

            logger.info("使用提示词管理器生成世界观")
            response_text = await llm_provider.generate_text(prompt)

            # 检查响应是否为空
            if not response_text or len(response_text.strip()) == 0:
                logger.error("LLM返回空响应")
                raise Exception("AI生成世界观失败：返回空响应")

            logger.info(f"LLM返回响应长度: {len(response_text)} 字符")
            logger.info(f"LLM响应内容预览: {response_text[:200]}...")

            result = self._parse_worldview_response(response_text)

            # 验证解析结果
            if not result or len(result) == 0:
                logger.error("世界观解析失败，结果为空")
                raise Exception("AI生成世界观失败：解析结果为空")

            return result

        except Exception as e:
            logger.error(f"AI智能生成世界观失败: {e}")
            raise Exception(f"LLM API调用失败: {str(e)}")

    def _parse_worldview_response(self, response_text: str) -> Dict[str, Any]:
        """解析纯世界观响应文本"""
        try:
            logger.info("开始解析世界观响应")

            # 尝试提取JSON
            json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
            if json_match:
                try:
                    result = json.loads(json_match.group())
                    logger.info(f"成功解析JSON，包含 {len(result)} 个字段")
                    return result
                except json.JSONDecodeError as e:
                    logger.error(f"JSON解析失败: {e}")
                    logger.error(f"尝试解析的JSON: {json_match.group()[:500]}...")

            # 如果无法解析JSON，尝试从文本中提取信息
            logger.warning("无法解析JSON，尝试从文本中提取信息")

            # 简单的文本解析
            lines = response_text.strip().split('\n')
            extracted_info = {}

            for line in lines:
                line = line.strip()
                if ':' in line and len(line) > 10:
                    key, value = line.split(':', 1)
                    extracted_info[key.strip()] = value.strip()

            if len(extracted_info) > 0:
                logger.info(f"从文本中提取了 {len(extracted_info)} 个字段")
                return {"extracted_content": extracted_info, "raw_text": response_text}

            # 如果完全无法解析，抛出错误
            logger.error("完全无法解析世界观响应")
            logger.error(f"原始响应: {response_text}")
            raise Exception(f"无法解析世界观响应，原始内容: {response_text[:200]}...")

        except Exception as e:
            logger.error(f"解析世界观响应失败: {e}")
            raise Exception(f"世界观解析失败: {str(e)}")
    

    async def generate_chapter_stream(self, story_data: Dict[str, Any], worldview_context: str = None, previous_choice: str = None, provider: str = None, user_choice: str = None):
        """流式生成章节内容"""
        logger.info(f"开始流式生成章节 - 故事: {story_data.get('title', 'Unknown')}, 章节: {story_data.get('current_chapter_number', 1)}, Provider: {provider}, 用户选择: {user_choice}")

        # 根据provider参数选择LLM Provider
        llm_provider = self._get_provider(provider)
        if not llm_provider:
            logger.error("LLM Provider未配置或配置错误")
            raise Exception("LLM Provider未配置或配置错误，无法生成内容")

        # 设置章节生成的高创作性Temperature
        from app.config import settings
        llm_provider.config['temperature'] = settings.chapter_temperature
        logger.info(f"🎨 章节生成使用高创作性配置 - Temperature: {settings.chapter_temperature}")
        
        try:
            # 处理style字段，可能是枚举值或字符串
            style_value = story_data['style']
            if isinstance(style_value, StoryStyle):
                style = style_value
            else:
                style = StoryStyle(style_value)

            # 使用统一提示词管理器构建上下文和提示词
            context = self._build_context(story_data, worldview_context, previous_choice)
            prompt = unified_prompt_manager.get_chapter_prompt(
                style=style,
                context=context,
                min_words=settings.min_chapter_length,
                max_words=settings.max_chapter_length
            )
            
            # 使用新的LLM抽象层进行流式生成
            accumulated_content = ""
            chapter_number = story_data.get('current_chapter_number', 1)
            default_title = f"第{chapter_number}章"
            extracted_title = None
            title_sent = False

            try:
                # 使用流式生成
                async for chunk in llm_provider.generate_stream(prompt):
                    # 安全处理chunk内容
                    chunk_content = chunk.content if chunk.content is not None else ""

                    if chunk_content:  # 只有非空内容才处理
                        accumulated_content += chunk_content

                        # 首次发送标题
                        if not title_sent:
                            # 尝试从累积内容中提取标题
                            if "CHAPTER_TITLE:" in accumulated_content:  # 检查是否包含标题标记
                                temp_title, _ = self._extract_chapter_title_and_content(accumulated_content)
                                if temp_title:
                                    extracted_title = temp_title

                            # 发送标题
                            final_title = extracted_title if extracted_title else default_title
                            yield StreamChunk(
                                type="title",
                                content=final_title,
                                metadata={'provider': provider or 'default'}
                            )
                            title_sent = True

                        # 清理内容，移除标题标记
                        cleaned_chunk = chunk_content
                        if "CHAPTER_TITLE:" in chunk_content:
                            _, cleaned_chunk = self._extract_chapter_title_and_content(chunk_content)

                        # 发送清理后的内容块
                        if cleaned_chunk.strip():  # 只发送非空内容
                            yield StreamChunk(
                                type="content",
                                content=cleaned_chunk,
                                metadata={'provider': provider or 'default'}
                            )

                    # 检查是否完成
                    if (hasattr(chunk, 'is_final') and chunk.is_final) or \
                       (hasattr(chunk, 'metadata') and chunk.metadata and chunk.metadata.get('is_final')) or \
                       (hasattr(chunk, 'type') and chunk.type == "complete"):
                        break

                # 提取标题和清理内容
                extracted_title, final_content = self._extract_chapter_title_and_content(accumulated_content.strip())

                # 确定最终标题
                if extracted_title and not title_sent:
                    # 如果还没发送标题且提取到了标题，使用提取的标题
                    final_title = extracted_title
                elif extracted_title:
                    # 如果已经发送了标题，更新最终标题用于保存
                    final_title = extracted_title
                else:
                    # 使用默认标题
                    final_title = default_title
                # 调整字数要求，增加容错性
                min_length = 1200  # 降低最低要求
                if not final_content or len(final_content) < min_length:
                    logger.error(f"生成的章节内容过短：{len(final_content)} 字符")
                    logger.error(f"内容预览：{final_content[:200]}")
                    raise Exception(f"章节内容生成失败：内容过短（{len(final_content)}字符），要求至少{min_length}字符")
                elif len(final_content) < 1500:
                    logger.warning(f"章节内容略短：{len(final_content)} 字符，建议长度1500+字符，但仍可接受")

                # 检查内容是否被截断（结尾不完整）
                if not final_content.endswith(('，', '。', '！', '？', '"', '"', '…', '……')):
                    logger.warning(f"章节内容可能被截断，结尾字符：'{final_content[-20:]}'")
                    # 如果内容太短且被截断，抛出错误
                    if len(final_content) < 2500:
                        raise Exception(f"章节内容被截断且过短（{len(final_content)}字符），需要重新生成")
                    else:
                        # 内容足够长但被截断，尝试添加合适的结尾
                        logger.info("内容足够长但被截断，尝试修复结尾")
                        if final_content.endswith(('的', '了', '着', '过', '在', '是', '有', '也', '都', '就', '要', '会', '能', '可', '不')):
                            final_content += "。"
                        elif final_content.endswith(('，', '、')):
                            final_content = final_content[:-1] + "。"
                        else:
                            final_content += "……"
                        accumulated_content = final_content

                # 发送内容完成信号（但还需要生成选择选项）
                yield StreamChunk(
                    type="content_complete",
                    content="",
                    metadata={
                        'title': final_title,
                        'content': final_content,
                        'provider': provider or 'default'
                    }
                )

                # 生成选择选项（如果需要）
                if story_data.get('generate_choices', True):
                    logger.info("开始生成选择选项...")
                    try:
                        choices = await self.generate_choices(
                            accumulated_content.strip(),
                            StoryStyle(story_data['style'])
                        )
                        logger.info(f"选择选项生成成功: {choices}")

                        # 发送最终完成信号，包含选择选项
                        yield StreamChunk(
                            type="complete",
                            content="",
                            metadata={
                                'title': final_title,
                                'content': final_content,
                                'choices': choices,
                                'provider': provider or 'default'
                            }
                        )
                        logger.info("章节完成信号已发送")
                    except Exception as e:
                        logger.error(f"生成选择选项失败: {e}")
                        # 提供备用选择选项，确保章节能够保存
                        backup_choices = [
                            "继续探索前方的道路",
                            "停下来仔细观察周围环境",
                            "寻找其他可能的路径"
                        ]
                        logger.info(f"使用备用选择选项: {backup_choices}")

                        # 即使选择选项生成失败，也要发送完成信号
                        yield StreamChunk(
                            type="complete",
                            content="",
                            metadata={
                                'title': final_title,
                                'content': final_content,
                                'choices': backup_choices,
                                'provider': provider or 'default'
                            }
                        )
                        logger.info("章节完成信号已发送（使用备用选择）")
                else:
                    # 如果不需要生成选择选项，直接发送完成信号
                    yield StreamChunk(
                        type="complete",
                        content="",
                        metadata={
                            'title': final_title,
                            'content': final_content,
                            'choices': [],
                            'provider': provider or 'default'
                        }
                    )
                    logger.info("章节完成信号已发送（无选择选项）")

            except Exception as e:
                logger.error(f"流式生成失败: {e}")

                # 检查是否有部分内容可以保存
                if accumulated_content and len(accumulated_content.strip()) > 1000:
                    logger.warning("流式生成失败，但有部分内容可以保存，尝试保存部分内容")

                    # 提取标题和内容
                    extracted_title, final_content = self._extract_chapter_title_and_content(accumulated_content.strip())

                    # 确定标题
                    if extracted_title:
                        final_title = extracted_title
                    else:
                        final_title = default_title

                    # 确保内容以合适的标点结尾
                    if final_content and not final_content.endswith(('，', '。', '！', '？', '"', '"', '…', '……')):
                        if final_content.endswith(('的', '了', '着', '过', '在', '是', '有', '也', '都', '就', '要', '会', '能', '可', '不')):
                            final_content += "。"
                        else:
                            final_content += "……"

                    # 发送内容完成信号
                    yield {
                        "type": "content_complete",
                        "title": final_title,
                        "content": final_content
                    }

                    # 提供备用选择选项并发送完成信号
                    backup_choices = [
                        "继续探索前方的道路",
                        "停下来仔细观察周围环境",
                        "寻找其他可能的路径"
                    ]

                    yield {
                        "type": "chapter_complete",
                        "data": {
                            "title": final_title,
                            "content": final_content,
                            "choices": backup_choices
                        }
                    }
                    logger.info("流式生成失败但已保存部分内容")
                else:
                    # 如果没有足够的内容，抛出错误
                    raise Exception(f"章节流式生成失败且内容不足: {str(e)}")

        except Exception as e:
            logger.error(f"AI流式生成章节失败: {e}")
            yield {
                "type": "error",
                "message": f"LLM API调用失败: {str(e)}"
            }
    
    async def generate_chapter_summary(self, chapter_content: str, provider: str = None) -> str:
        """生成章节摘要"""
        logger.info(f"开始生成章节摘要 - Provider: {provider}")

        # 根据provider参数选择LLM Provider
        llm_provider = self._get_provider(provider)
        if not llm_provider:
            logger.error("LLM Provider未配置或配置错误")
            raise Exception("LLM Provider未配置或配置错误，无法生成摘要")

        # 设置摘要生成的低创作性Temperature（保持准确性）
        from app.config import settings
        llm_provider.config['temperature'] = settings.summary_temperature
        logger.info(f"📝 摘要生成使用低创作性配置 - Temperature: {settings.summary_temperature}")

        try:
            # 使用统一提示词管理器生成摘要提示词
            prompt = unified_prompt_manager.get_summary_prompt(chapter_content)

            # 生成摘要
            summary = await llm_provider.generate_text(prompt)

            # 清理摘要文本
            summary = summary.strip()
            if summary.startswith('"') and summary.endswith('"'):
                summary = summary[1:-1]

            logger.info(f"章节摘要生成完成: {summary[:50]}...")
            return summary

        except Exception as e:
            logger.error(f"生成章节摘要失败: {e}")
            raise Exception(f"生成章节摘要失败: {str(e)}")

    async def generate_choices(self, chapter_content: str, story_style: StoryStyle, provider: str = None) -> List[str]:
        """生成选择选项"""
        logger.info(f"开始生成选择选项 - 风格: {story_style.value}, Provider: {provider}")

        # 根据provider参数选择LLM Provider
        llm_provider = self._get_provider(provider)
        if not llm_provider:
            logger.error("LLM Provider未配置或配置错误")
            raise Exception("LLM Provider未配置或配置错误，无法生成选择")

        # 设置选择选项生成的中等创作性Temperature
        from app.config import settings
        llm_provider.config['temperature'] = settings.choices_temperature
        logger.info(f"🎯 选择选项生成使用中等创作性配置 - Temperature: {settings.choices_temperature}")
        
        try:
            # 使用统一提示词管理器生成选择提示词
            prompt = unified_prompt_manager.get_choices_prompt(chapter_content, story_style)

            # 使用新的LLM抽象层生成选择
            try:
                response_text = await llm_provider.generate_text(prompt)
                logger.info(f"AI返回的原始选择内容: {response_text}")

                # 尝试解析JSON数组 - 改进正则表达式支持多行
                json_match = re.search(r'\[[\s\S]*?\]', response_text)
                logger.info(f"正则匹配结果: {json_match.group() if json_match else 'None'}")

                if json_match:
                    try:
                        choices = json.loads(json_match.group())
                        if isinstance(choices, list) and len(choices) >= 3:
                            logger.info(f"成功解析选择选项: {choices}")
                            return choices[:3]
                    except json.JSONDecodeError as e:
                        logger.error(f"JSON解析错误: {e}")

                # 备用解析方法：尝试从文本中提取选择
                logger.warning("JSON解析失败，尝试备用解析方法")
                fallback_choices = self._extract_choices_fallback(response_text)
                if fallback_choices and len(fallback_choices) >= 3:
                    logger.info(f"备用方法成功提取选择: {fallback_choices}")
                    return fallback_choices[:3]

                # 如果所有方法都失败，抛出错误
                logger.error("无法解析选择选项的JSON格式")
                logger.error(f"AI原始响应: {response_text}")
                raise Exception("选择选项解析失败：无法从AI响应中提取有效的选择选项")

            except Exception as e:
                logger.error(f"生成选择失败: {e}")
                raise Exception(f"选择选项生成失败: {str(e)}")

        except Exception as e:
            logger.error(f"AI生成选择失败: {e}")
            raise Exception(f"LLM API调用失败: {str(e)}")





    def _extract_choices_fallback(self, response_text: str) -> List[str]:
        """备用选择提取方法"""
        try:
            # 方法1: 查找引号包围的文本
            import re

            # 查找所有引号包围的文本
            quoted_texts = re.findall(r'"([^"]{10,50})"', response_text)
            if len(quoted_texts) >= 3:
                return quoted_texts[:3]

            # 方法2: 按行分割，查找选择格式
            lines = response_text.split('\n')
            choices = []

            for line in lines:
                line = line.strip()
                # 查找类似 "1. xxx" 或 "- xxx" 或 "• xxx" 的格式
                if re.match(r'^[\d\-•]\s*[\.、]\s*(.+)', line):
                    choice = re.sub(r'^[\d\-•]\s*[\.、]\s*', '', line).strip()
                    if 5 <= len(choice) <= 50:  # 合理的选择长度
                        choices.append(choice)
                # 或者直接是引号包围的文本
                elif line.startswith('"') and line.endswith('"'):
                    choice = line.strip('"')
                    if 5 <= len(choice) <= 50:
                        choices.append(choice)

            if len(choices) >= 3:
                return choices[:3]

            # 方法3: 生成默认选择
            logger.warning("无法从AI响应中提取选择，使用默认选择")
            return [
                "继续前进，探索未知",
                "谨慎观察，寻找线索",
                "暂时退后，重新考虑"
            ]

        except Exception as e:
            logger.error(f"备用选择提取失败: {e}")
            return [
                "继续前进，探索未知",
                "谨慎观察，寻找线索",
                "暂时退后，重新考虑"
            ]


# 创建全局AI服务实例
ai_service = AIService()