from typing import List, Dict, Any, AsyncGenerator
import json
import re
from app.config import settings
from app.models import StoryStyle
from app.utils.logger import get_logger
from app.services.llm.providers import LLMProviderFactory
from app.services.llm.base import LLMProviderError, StreamChunk
from app.services.llm.models.pure_worldview_models import PureWorldView

logger = get_logger(__name__)

class AIService:
    def __init__(self, default_provider: str = None):
        """初始化AI服务，使用新的LLM抽象层"""
        try:
            # 使用指定的默认provider或系统默认
            if default_provider:
                self.llm_provider = LLMProviderFactory.create_provider(default_provider)
                self.default_provider = default_provider
            else:
                self.llm_provider = LLMProviderFactory.create_default_provider()
                self.default_provider = None
            logger.info(f"AI服务初始化成功，使用Provider: {self.llm_provider.provider_type.value}")
        except Exception as e:
            logger.error(f"AI服务初始化失败: {e}")
            self.llm_provider = None
            self.default_provider = None

    def _get_provider(self, provider: str = None):
        """根据provider参数获取LLM Provider"""
        # 优先使用传入的provider，然后是default_provider，最后是当前provider
        target_provider = provider or self.default_provider
        if target_provider:
            try:
                from app.services.llm.base import LLMProviderType

                # 映射provider字符串到枚举
                provider_map = {
                    'gemini': LLMProviderType.GEMINI,
                    'siliconflow': LLMProviderType.SILICONFLOW
                }

                if target_provider.lower() in provider_map:
                    provider_type = provider_map[target_provider.lower()]
                    return LLMProviderFactory.create_provider(provider_type)
                else:
                    logger.warning(f"未知的provider: {target_provider}，使用默认provider")
                    return self.llm_provider
            except Exception as e:
                logger.error(f"创建provider失败: {e}，使用默认provider")
                return self.llm_provider
        else:
            # 使用默认provider
            return self.llm_provider

    def _extract_chapter_title_and_content(self, content: str) -> tuple[str, str]:
        """提取章节标题和内容"""
        import re

        content = content.strip()

        # 查找章节标题标记
        title_match = re.match(r'^CHAPTER_TITLE:\s*(.+?)(?:\n|$)', content, re.MULTILINE)

        if title_match:
            # 提取标题
            chapter_title = title_match.group(1).strip()
            # 移除标题行，获取正文
            content_without_title = re.sub(r'^CHAPTER_TITLE:\s*.+?(?:\n|$)', '', content, flags=re.MULTILINE).strip()
        else:
            # 如果没有找到标题标记，使用默认标题
            chapter_title = None
            content_without_title = content

        # 清理内容中可能残留的格式化标记
        cleaned_content = self._clean_chapter_content(content_without_title)

        return chapter_title, cleaned_content

    def _clean_chapter_content(self, content: str) -> str:
        """清理章节内容，移除格式化标记"""
        import re

        # 移除章节标题标记（如：**第X章：标题**、第X章、**第X章**等）
        patterns = [
            r'^\*\*第\d+章[：:][^*]*\*\*\s*',  # **第X章：标题**
            r'^\*\*第\d+章\*\*\s*',           # **第X章**
            r'^第\d+章[：:][^\n]*\n',         # 第X章：标题
            r'^第\d+章\s*\n',                 # 第X章
            r'^\s*第\d+章\s*',                # 开头的第X章
        ]

        cleaned_content = content.strip()
        for pattern in patterns:
            cleaned_content = re.sub(pattern, '', cleaned_content, flags=re.MULTILINE)

        return cleaned_content.strip()

    def _get_style_prompt(self, style: StoryStyle) -> str:
        """根据故事风格获取对应的prompt模板"""
        prompts = {
            StoryStyle.XIANXIA: """
你是一位专业的修仙小说作家。请创作一个修仙风格的故事章节，包含以下元素：
- 古典仙侠世界观，包含修炼体系、门派、法宝等元素
- 生动的人物描写和对话
- 引人入胜的情节发展
- 适当的悬念和冲突
章节长度应在2000-3000字之间。
""",
            StoryStyle.WUXIA: """
你是一位专业的武侠小说作家。请创作一个武侠风格的故事章节，包含以下元素：
- 江湖世界观，包含武功、门派、恩怨情仇
- 侠义精神和江湖道义
- 精彩的武打场面描写
- 人物性格鲜明，对话生动
章节长度应在2000-3000字之间。
""",
            StoryStyle.SCIFI: """
你是一位专业的科幻小说作家。请创作一个科技风格的故事章节，包含以下元素：
- 未来科技世界观，包含先进科技、太空探索、AI文明
- 科学幻想与人文思考的结合
- 紧张刺激的情节发展
- 对未来社会的深度思考
章节长度应在2000-3000字之间。
"""
        }
        return prompts.get(style, prompts[StoryStyle.XIANXIA])
    
    def _build_context(self, story_data: Dict[str, Any], worldview_context: str = None, previous_choice: str = None) -> str:
        """构建故事上下文"""
        context = f"故事风格：{story_data.get('style', '')}\n"
        context += f"故事标题：{story_data.get('title', '')}\n"
        context += f"当前章节：第{story_data.get('current_chapter_number', 1)}章\n"

        # 添加世界观框架
        if worldview_context:
            context += f"\n世界观框架：\n{worldview_context}\n"

        # 添加章节摘要历史
        if story_data.get('chapter_summaries'):
            summaries = story_data['chapter_summaries']
            logger.info(f"📚 使用历史章节摘要生成新章节，共 {len(summaries)} 个章节")

            context += "\n之前的故事情节：\n"
            used_chapters = []

            for summary_data in summaries:
                if isinstance(summary_data, dict):
                    chapter_num = summary_data.get('chapter_number', 0)
                    title = summary_data.get('title', '')
                    summary = summary_data.get('summary', '')
                    choices = summary_data.get('choices', [])

                    context += f"第{chapter_num}章 {title}：{summary}\n"
                    if choices:
                        context += f"  选择选项：{', '.join(choices)}\n"

                    used_chapters.append(f"第{chapter_num}章《{title}》")
                else:
                    # 兼容旧格式
                    context += f"章节摘要：{summary_data}\n"
                    used_chapters.append("历史章节")

            logger.info(f"📖 具体使用的章节摘要: {' | '.join(used_chapters)}")
        else:
            logger.info("📝 当前为第一章，无历史摘要可用")

        # 添加角色信息
        if story_data.get('character_info'):
            context += "\n主要角色：\n"
            for name, info in story_data['character_info'].items():
                context += f"{name}：{info}\n"

        # 添加用户选择（优先使用story_data中的user_choice）
        user_choice = story_data.get('user_choice') or previous_choice
        if user_choice:
            context += f"\n用户在上一章的选择：{user_choice}\n"
            context += "请根据这个选择继续故事发展，确保情节的连贯性和逻辑性。\n"

        return context
    
    # ❌ 已移除：generate_worldview 旧方法
    #
    # 原因：
    # 1. 需要用户提供主题，增加使用复杂度
    # 2. 功能与 generate_worldview_auto 重复
    # 3. 不符合智能化的产品方向
    # 4. 简化API，统一使用自动生成方法
    #
    # 替代方案：统一使用 generate_worldview_auto



    async def generate_worldview_auto(self, story_title: str, story_style: StoryStyle, provider: str = None) -> Dict[str, Any]:
        """智能生成世界观：AI根据标题和风格自动创建完整世界观"""
        logger.info(f"开始智能生成世界观 - 标题: {story_title}, 风格: {story_style.value}, Provider: {provider}")

        # 根据provider参数选择LLM Provider
        llm_provider = self._get_provider(provider)
        if not llm_provider:
            logger.error("LLM Provider未配置或配置错误")
            raise Exception("LLM Provider未配置或配置错误，无法生成世界观")

        # 设置世界观生成的中高创作性Temperature
        from app.config import settings
        llm_provider.config['temperature'] = settings.worldview_temperature
        logger.info(f"🌍 世界观生成使用中高创作性配置 - Temperature: {settings.worldview_temperature}")

        try:
            # 根据风格和标题智能生成世界观，AI自动推断主题
            if story_style == StoryStyle.XIANXIA:
                prompt = f"""
为修仙小说《{story_title}》创建完整的世界观框架。请根据标题自动推断故事主题和风格。

请返回JSON格式：
{{
  "world_name": "世界名称（如：青云大陆）",
  "power_system": "修炼体系详述（包含境界划分）",
  "geography": "主要地理环境和重要地点",
  "main_character": "主角基础设定和背景",
  "story_theme": "根据标题推断的故事主题",
  "main_plot": "主要情节线索",
  "conflict_setup": "核心矛盾冲突"
}}

要求：
1. 根据标题《{story_title}》智能推断故事主题和背景
2. 创建符合修仙风格的完整世界观
3. 每个字段内容丰富但简洁，100-200字
4. 为故事发展预留空间
"""
            elif story_style == StoryStyle.WUXIA:
                prompt = f"""
为武侠小说《{story_title}》创建完整的江湖世界观。请根据标题自动推断故事主题和风格。

请返回JSON格式：
{{
  "world_name": "江湖背景（如：大明江湖、宋朝武林）",
  "power_system": "武功体系和门派设定",
  "geography": "主要地理环境和江湖重地",
  "main_character": "主角基础设定和江湖身份",
  "story_theme": "根据标题推断的故事主题",
  "main_plot": "主要情节线索",
  "conflict_setup": "江湖恩怨和核心冲突"
}}

要求：
1. 根据标题《{story_title}》智能推断故事主题和背景
2. 创建符合武侠风格的完整世界观
3. 每个字段内容丰富但简洁，100-200字
4. 体现江湖特色和武侠精神
"""
            else:  # SCIFI
                prompt = f"""
为科幻小说《{story_title}》创建完整的世界观框架。请根据标题自动推断故事主题和风格。

请返回JSON格式：
{{
  "world_name": "世界背景（如：2157年地球、银河联邦）",
  "power_system": "科技体系和能力设定",
  "geography": "主要环境和重要地点",
  "main_character": "主角基础设定和身份背景",
  "story_theme": "根据标题推断的故事主题",
  "main_plot": "主要情节线索",
  "conflict_setup": "科技冲突和核心矛盾"
}}

要求：
1. 根据标题《{story_title}》智能推断故事主题和背景
2. 创建符合科幻风格的完整世界观
3. 每个字段内容丰富但简洁，100-200字
4. 体现科技感和未来特色
"""

            logger.info("使用智能世界观生成策略")
            response_text = await llm_provider.generate_text(prompt)

            # 检查响应是否为空
            if not response_text or len(response_text.strip()) == 0:
                logger.error("LLM返回空响应")
                raise Exception("AI生成世界观失败：返回空响应")

            logger.info(f"LLM返回响应长度: {len(response_text)} 字符")
            logger.info(f"LLM响应内容预览: {response_text[:200]}...")

            result = self._parse_worldview_response(response_text)

            # 验证解析结果
            if not result or len(result) == 0:
                logger.error("世界观解析失败，结果为空")
                raise Exception("AI生成世界观失败：解析结果为空")

            return result

        except Exception as e:
            logger.error(f"AI智能生成世界观失败: {e}")
            raise Exception(f"LLM API调用失败: {str(e)}")

    def _parse_worldview_response(self, response_text: str) -> Dict[str, Any]:
        """解析纯世界观响应文本"""
        try:
            logger.info("开始解析世界观响应")

            # 尝试提取JSON
            json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
            if json_match:
                try:
                    result = json.loads(json_match.group())
                    logger.info(f"成功解析JSON，包含 {len(result)} 个字段")
                    return result
                except json.JSONDecodeError as e:
                    logger.error(f"JSON解析失败: {e}")
                    logger.error(f"尝试解析的JSON: {json_match.group()[:500]}...")

            # 如果无法解析JSON，尝试从文本中提取信息
            logger.warning("无法解析JSON，尝试从文本中提取信息")

            # 简单的文本解析
            lines = response_text.strip().split('\n')
            extracted_info = {}

            for line in lines:
                line = line.strip()
                if ':' in line and len(line) > 10:
                    key, value = line.split(':', 1)
                    extracted_info[key.strip()] = value.strip()

            if len(extracted_info) > 0:
                logger.info(f"从文本中提取了 {len(extracted_info)} 个字段")
                return {"extracted_content": extracted_info, "raw_text": response_text}

            # 如果完全无法解析，抛出错误
            logger.error("完全无法解析世界观响应")
            logger.error(f"原始响应: {response_text}")
            raise Exception(f"无法解析世界观响应，原始内容: {response_text[:200]}...")

        except Exception as e:
            logger.error(f"解析世界观响应失败: {e}")
            raise Exception(f"世界观解析失败: {str(e)}")
    

    async def generate_chapter_stream(self, story_data: Dict[str, Any], worldview_context: str = None, previous_choice: str = None, provider: str = None, user_choice: str = None):
        """流式生成章节内容"""
        logger.info(f"开始流式生成章节 - 故事: {story_data.get('title', 'Unknown')}, 章节: {story_data.get('current_chapter_number', 1)}, Provider: {provider}, 用户选择: {user_choice}")

        # 根据provider参数选择LLM Provider
        llm_provider = self._get_provider(provider)
        if not llm_provider:
            logger.error("LLM Provider未配置或配置错误")
            raise Exception("LLM Provider未配置或配置错误，无法生成内容")

        # 设置章节生成的高创作性Temperature
        from app.config import settings
        llm_provider.config['temperature'] = settings.chapter_temperature
        logger.info(f"🎨 章节生成使用高创作性配置 - Temperature: {settings.chapter_temperature}")
        
        try:
            # 处理style字段，可能是枚举值或字符串
            style_value = story_data['style']
            if isinstance(style_value, StoryStyle):
                style = style_value
            else:
                style = StoryStyle(style_value)
            style_prompt = self._get_style_prompt(style)
            context = self._build_context(story_data, worldview_context, previous_choice)
            
            prompt = f"""{style_prompt}

{context}

请基于以上信息，创作下一章节的内容。

**输出格式要求（必须严格遵守）：**
首先输出章节标题，格式为：
CHAPTER_TITLE: 具体的章节标题（如：血祭危机、乾坤印觉醒、绝境逃生等）

然后输出章节正文内容，直接从故事开始，不要包含任何格式化标记。

**内容要求（必须遵守）：**
1. **字数要求**：章节内容必须在2500-3000字之间，这是硬性要求！
2. **内容质量**：必须是完整的故事章节，包含详细的情节描述、人物对话、环境描写
3. **故事连贯**：与之前的情节自然衔接，体现用户选择的影响
4. **结构完整**：有明确的开头、发展、高潮、结尾
5. **悬念设置**：章节结尾要为下一个选择做铺垫
6. **标题要求**：章节标题要简洁有力，体现本章核心情节，不超过8个字

**重要提醒**：
- 请确保生成足够长的内容，至少2500字！不要过早结束！
- 章节标题要放在最开头，格式：CHAPTER_TITLE: 标题内容
- 正文内容不要包含"第X章"、"**第X章：标题**"等格式化标记
- 直接从故事正文开始"""
            
            # 使用新的LLM抽象层进行流式生成
            accumulated_content = ""
            chapter_number = story_data.get('current_chapter_number', 1)
            default_title = f"第{chapter_number}章"
            extracted_title = None
            title_sent = False

            try:
                # 使用流式生成
                async for chunk in llm_provider.generate_stream(prompt):
                    # 安全处理chunk内容
                    chunk_content = chunk.content if chunk.content is not None else ""

                    if chunk_content:  # 只有非空内容才处理
                        accumulated_content += chunk_content

                        # 首次发送标题
                        if not title_sent:
                            # 尝试从累积内容中提取标题
                            if "CHAPTER_TITLE:" in accumulated_content:  # 检查是否包含标题标记
                                temp_title, _ = self._extract_chapter_title_and_content(accumulated_content)
                                if temp_title:
                                    extracted_title = temp_title

                            # 发送标题
                            final_title = extracted_title if extracted_title else default_title
                            yield StreamChunk(
                                type="title",
                                content=final_title,
                                metadata={'provider': provider or 'default'}
                            )
                            title_sent = True

                        # 清理内容，移除标题标记
                        cleaned_chunk = chunk_content
                        if "CHAPTER_TITLE:" in chunk_content:
                            _, cleaned_chunk = self._extract_chapter_title_and_content(chunk_content)

                        # 发送清理后的内容块
                        if cleaned_chunk.strip():  # 只发送非空内容
                            yield StreamChunk(
                                type="content",
                                content=cleaned_chunk,
                                metadata={'provider': provider or 'default'}
                            )

                    # 检查是否完成
                    if (hasattr(chunk, 'is_final') and chunk.is_final) or \
                       (hasattr(chunk, 'metadata') and chunk.metadata and chunk.metadata.get('is_final')) or \
                       (hasattr(chunk, 'type') and chunk.type == "complete"):
                        break

                # 提取标题和清理内容
                extracted_title, final_content = self._extract_chapter_title_and_content(accumulated_content.strip())

                # 确定最终标题
                if extracted_title and not title_sent:
                    # 如果还没发送标题且提取到了标题，使用提取的标题
                    final_title = extracted_title
                elif extracted_title:
                    # 如果已经发送了标题，更新最终标题用于保存
                    final_title = extracted_title
                else:
                    # 使用默认标题
                    final_title = default_title
                if not final_content or len(final_content) < 1500:
                    logger.error(f"生成的章节内容过短：{len(final_content)} 字符")
                    logger.error(f"内容预览：{final_content[:200]}")
                    raise Exception(f"章节内容生成失败：内容过短（{len(final_content)}字符），要求至少1500字符")

                # 检查内容是否被截断（结尾不完整）
                if not final_content.endswith(('，', '。', '！', '？', '"', '"', '…', '……')):
                    logger.warning(f"章节内容可能被截断，结尾字符：'{final_content[-20:]}'")
                    # 如果内容太短且被截断，抛出错误
                    if len(final_content) < 2500:
                        raise Exception(f"章节内容被截断且过短（{len(final_content)}字符），需要重新生成")
                    else:
                        # 内容足够长但被截断，尝试添加合适的结尾
                        logger.info("内容足够长但被截断，尝试修复结尾")
                        if final_content.endswith(('的', '了', '着', '过', '在', '是', '有', '也', '都', '就', '要', '会', '能', '可', '不')):
                            final_content += "。"
                        elif final_content.endswith(('，', '、')):
                            final_content = final_content[:-1] + "。"
                        else:
                            final_content += "……"
                        accumulated_content = final_content

                # 发送内容完成信号（但还需要生成选择选项）
                yield StreamChunk(
                    type="content_complete",
                    content="",
                    metadata={
                        'title': final_title,
                        'content': final_content,
                        'provider': provider or 'default'
                    }
                )

                # 生成选择选项（如果需要）
                if story_data.get('generate_choices', True):
                    logger.info("开始生成选择选项...")
                    try:
                        choices = await self.generate_choices(
                            accumulated_content.strip(),
                            StoryStyle(story_data['style'])
                        )
                        logger.info(f"选择选项生成成功: {choices}")

                        # 发送最终完成信号，包含选择选项
                        yield StreamChunk(
                            type="complete",
                            content="",
                            metadata={
                                'title': final_title,
                                'content': final_content,
                                'choices': choices,
                                'provider': provider or 'default'
                            }
                        )
                        logger.info("章节完成信号已发送")
                    except Exception as e:
                        logger.error(f"生成选择选项失败: {e}")
                        # 提供备用选择选项，确保章节能够保存
                        backup_choices = [
                            "继续探索前方的道路",
                            "停下来仔细观察周围环境",
                            "寻找其他可能的路径"
                        ]
                        logger.info(f"使用备用选择选项: {backup_choices}")

                        # 即使选择选项生成失败，也要发送完成信号
                        yield StreamChunk(
                            type="complete",
                            content="",
                            metadata={
                                'title': final_title,
                                'content': final_content,
                                'choices': backup_choices,
                                'provider': provider or 'default'
                            }
                        )
                        logger.info("章节完成信号已发送（使用备用选择）")
                else:
                    # 如果不需要生成选择选项，直接发送完成信号
                    yield StreamChunk(
                        type="complete",
                        content="",
                        metadata={
                            'title': final_title,
                            'content': final_content,
                            'choices': [],
                            'provider': provider or 'default'
                        }
                    )
                    logger.info("章节完成信号已发送（无选择选项）")

            except Exception as e:
                logger.error(f"流式生成失败: {e}")

                # 检查是否有部分内容可以保存
                if accumulated_content and len(accumulated_content.strip()) > 1000:
                    logger.warning("流式生成失败，但有部分内容可以保存，尝试保存部分内容")

                    # 提取标题和内容
                    extracted_title, final_content = self._extract_chapter_title_and_content(accumulated_content.strip())

                    # 确定标题
                    if extracted_title:
                        final_title = extracted_title
                    else:
                        final_title = default_title

                    # 确保内容以合适的标点结尾
                    if final_content and not final_content.endswith(('，', '。', '！', '？', '"', '"', '…', '……')):
                        if final_content.endswith(('的', '了', '着', '过', '在', '是', '有', '也', '都', '就', '要', '会', '能', '可', '不')):
                            final_content += "。"
                        else:
                            final_content += "……"

                    # 发送内容完成信号
                    yield {
                        "type": "content_complete",
                        "title": final_title,
                        "content": final_content
                    }

                    # 提供备用选择选项并发送完成信号
                    backup_choices = [
                        "继续探索前方的道路",
                        "停下来仔细观察周围环境",
                        "寻找其他可能的路径"
                    ]

                    yield {
                        "type": "chapter_complete",
                        "data": {
                            "title": final_title,
                            "content": final_content,
                            "choices": backup_choices
                        }
                    }
                    logger.info("流式生成失败但已保存部分内容")
                else:
                    # 如果没有足够的内容，抛出错误
                    raise Exception(f"章节流式生成失败且内容不足: {str(e)}")

        except Exception as e:
            logger.error(f"AI流式生成章节失败: {e}")
            yield {
                "type": "error",
                "message": f"LLM API调用失败: {str(e)}"
            }
    
    async def generate_chapter_summary(self, chapter_content: str, provider: str = None) -> str:
        """生成章节摘要"""
        logger.info(f"开始生成章节摘要 - Provider: {provider}")

        # 根据provider参数选择LLM Provider
        llm_provider = self._get_provider(provider)
        if not llm_provider:
            logger.error("LLM Provider未配置或配置错误")
            raise Exception("LLM Provider未配置或配置错误，无法生成摘要")

        # 设置摘要生成的低创作性Temperature（保持准确性）
        from app.config import settings
        llm_provider.config['temperature'] = settings.summary_temperature
        logger.info(f"📝 摘要生成使用低创作性配置 - Temperature: {settings.summary_temperature}")

        try:
            prompt = f"""
请阅读以下完整章节内容，并用1-2句话总结其核心情节：

章节内容：
{chapter_content}

要求：
1. 总结整章的主要情节发展，不要复制开头文字
2. 突出关键事件和重要转折点
3. 不超过100字
4. 便于AI理解故事发展脉络
5. 直接返回摘要文本，不需要其他格式

示例格式：
"主角做了什么，发生了什么关键事件，结果如何。"
"""

            # 生成摘要
            summary = await llm_provider.generate_text(prompt)

            # 清理摘要文本
            summary = summary.strip()
            if summary.startswith('"') and summary.endswith('"'):
                summary = summary[1:-1]

            logger.info(f"章节摘要生成完成: {summary[:50]}...")
            return summary

        except Exception as e:
            logger.error(f"生成章节摘要失败: {e}")
            raise Exception(f"生成章节摘要失败: {str(e)}")

    async def generate_choices(self, chapter_content: str, story_style: StoryStyle, provider: str = None) -> List[str]:
        """生成选择选项"""
        logger.info(f"开始生成选择选项 - 风格: {story_style.value}, Provider: {provider}")

        # 根据provider参数选择LLM Provider
        llm_provider = self._get_provider(provider)
        if not llm_provider:
            logger.error("LLM Provider未配置或配置错误")
            raise Exception("LLM Provider未配置或配置错误，无法生成选择")

        # 设置选择选项生成的中等创作性Temperature
        from app.config import settings
        llm_provider.config['temperature'] = settings.choices_temperature
        logger.info(f"🎯 选择选项生成使用中等创作性配置 - Temperature: {settings.choices_temperature}")
        
        try:
            prompt = f"""
基于以下章节内容，生成3个不同的选择选项，让读者决定故事的发展方向。

章节内容：
{chapter_content}

要求：
1. 3个选择要有明显的差异，代表不同的发展方向
2. 选择要符合{story_style.value}风格
3. 每个选择都要简洁明了，不超过30字
4. 返回JSON格式的数组

示例格式：
["选择1", "选择2", "选择3"]
"""

            # 使用新的LLM抽象层生成选择
            try:
                # 尝试使用结构化输出
                from app.services.llm.models import ChapterGenerationRequest, ChapterResponse

                # 如果有结构化输出模型，使用它
                response_text = await llm_provider.generate_text(prompt)
                logger.info(f"AI返回的原始选择内容: {response_text}")

                # 尝试解析JSON数组 - 改进正则表达式支持多行
                json_match = re.search(r'\[[\s\S]*?\]', response_text)
                logger.info(f"正则匹配结果: {json_match.group() if json_match else 'None'}")

                if json_match:
                    try:
                        choices = json.loads(json_match.group())
                        if isinstance(choices, list) and len(choices) >= 3:
                            logger.info(f"成功解析选择选项: {choices}")
                            return choices[:3]
                    except json.JSONDecodeError as e:
                        logger.error(f"JSON解析错误: {e}")

                # 备用解析方法：尝试从文本中提取选择
                logger.warning("JSON解析失败，尝试备用解析方法")
                fallback_choices = self._extract_choices_fallback(response_text)
                if fallback_choices and len(fallback_choices) >= 3:
                    logger.info(f"备用方法成功提取选择: {fallback_choices}")
                    return fallback_choices[:3]

                # 如果所有方法都失败，抛出错误
                logger.error("无法解析选择选项的JSON格式")
                logger.error(f"AI原始响应: {response_text}")
                raise Exception("选择选项解析失败：无法从AI响应中提取有效的选择选项")

            except Exception as e:
                logger.error(f"生成选择失败: {e}")
                raise Exception(f"选择选项生成失败: {str(e)}")

        except Exception as e:
            logger.error(f"AI生成选择失败: {e}")
            raise Exception(f"LLM API调用失败: {str(e)}")





    def _extract_choices_fallback(self, response_text: str) -> List[str]:
        """备用选择提取方法"""
        try:
            # 方法1: 查找引号包围的文本
            import re

            # 查找所有引号包围的文本
            quoted_texts = re.findall(r'"([^"]{10,50})"', response_text)
            if len(quoted_texts) >= 3:
                return quoted_texts[:3]

            # 方法2: 按行分割，查找选择格式
            lines = response_text.split('\n')
            choices = []

            for line in lines:
                line = line.strip()
                # 查找类似 "1. xxx" 或 "- xxx" 或 "• xxx" 的格式
                if re.match(r'^[\d\-•]\s*[\.、]\s*(.+)', line):
                    choice = re.sub(r'^[\d\-•]\s*[\.、]\s*', '', line).strip()
                    if 5 <= len(choice) <= 50:  # 合理的选择长度
                        choices.append(choice)
                # 或者直接是引号包围的文本
                elif line.startswith('"') and line.endswith('"'):
                    choice = line.strip('"')
                    if 5 <= len(choice) <= 50:
                        choices.append(choice)

            if len(choices) >= 3:
                return choices[:3]

            # 方法3: 生成默认选择
            logger.warning("无法从AI响应中提取选择，使用默认选择")
            return [
                "继续前进，探索未知",
                "谨慎观察，寻找线索",
                "暂时退后，重新考虑"
            ]

        except Exception as e:
            logger.error(f"备用选择提取失败: {e}")
            return [
                "继续前进，探索未知",
                "谨慎观察，寻找线索",
                "暂时退后，重新考虑"
            ]


# 创建全局AI服务实例
ai_service = AIService()