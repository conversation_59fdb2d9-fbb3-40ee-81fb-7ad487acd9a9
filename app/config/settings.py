from pydantic_settings import BaseSettings
from typing import List, Union
import json
import os

class Settings(BaseSettings):
    # 应用配置
    app_name: str = "InkFlow AI"
    app_version: str = "1.0.0"
    debug: bool = False

    # 日志配置
    log_level: str = "INFO"
    show_sql_logs: bool = False

    # 数据库配置
    database_url: str = "postgresql://admin:admin@localhost:5432/ai_novel"

    # AI配置
    gemini_api_key: str = ""
    gemini_model: str = "gemini-2.5-flash"

    # 硅基流动配置
    siliconflow_api_key: str = ""
    siliconflow_base_url: str = "https://api.siliconflow.cn/v1"
    siliconflow_model: str = "Qwen/Qwen2.5-7B-Instruct"  # 默认使用免费模型

    # LLM配置
    default_llm_provider: str = "siliconflow"  # 改为默认使用硅基流动

    # API配置
    api_prefix: str = "/api/v1"
    cors_origins: Union[List[str], str] = ["*"]

    # 安全配置
    secret_key: str = "dev-secret-key"
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30

    # JWT配置（兼容性）
    jwt_secret_key: str = ""

    # 内容生成配置
    max_chapter_length: int = 3000
    min_chapter_length: int = 2000
    choices_count: int = 3

    # AI创作性配置
    # Temperature控制AI的创作性：0.0-2.0，值越高越有创意但可能不够连贯
    chapter_temperature: float = 0.9      # 章节生成：高创作性，让故事更有趣
    worldview_temperature: float = 0.8    # 世界观生成：中高创作性，保持设定合理
    summary_temperature: float = 0.3      # 摘要生成：低创作性，保持准确性
    choices_temperature: float = 0.7      # 选项生成：中等创作性，平衡多样性和合理性

    # Redis配置
    redis_url: str = "redis://localhost:6379/0"

    def __init__(self, **kwargs):
        super().__init__(**kwargs)

        # 处理CORS_ORIGINS字符串格式
        if isinstance(self.cors_origins, str):
            try:
                # 尝试解析JSON格式的字符串
                self.cors_origins = json.loads(self.cors_origins)
            except json.JSONDecodeError:
                # 如果不是JSON，按逗号分割
                self.cors_origins = [origin.strip() for origin in self.cors_origins.split(",")]

        # JWT密钥兼容性处理
        if not self.jwt_secret_key:
            self.jwt_secret_key = self.secret_key

    class Config:
        env_file_encoding = "utf-8"
        case_sensitive = False  # 环境变量不区分大小写

# 确定使用哪个环境文件
def get_env_file():
    if os.path.exists(".env.local"):
        print("🔧 使用本地开发配置: .env.local")
        return ".env.local"
    else:
        print("🔧 使用生产配置: .env")
        return ".env"

# 实例化配置
settings = Settings(_env_file=get_env_file())