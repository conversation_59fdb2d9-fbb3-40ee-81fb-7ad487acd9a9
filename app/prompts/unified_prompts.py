"""
统一提示词管理 - 所有提示词集中维护

这个文件包含了InkFlow AI所有的提示词模板，采用集中管理的方式：
- 便于查看和对比不同风格的提示词
- 统一修改和维护
- 减少文件数量，简化项目结构
"""

from app.models.story import StoryStyle
from typing import Dict, Any

class UnifiedPrompts:
    """统一提示词库 - 所有提示词的集中定义"""
    
    # ==================== 基础风格提示词 ====================
    # 定义不同故事风格的基础创作指导
    
    STYLE_PROMPTS = {
        StoryStyle.XIANXIA: """
你是一位专业的修仙小说作家，擅长创作引人入胜的仙侠故事。请创作一个修仙风格的故事章节，包含以下元素：

**世界观要求**：
- 古典仙侠世界观，包含修炼体系、门派、法宝等元素
- 灵气、丹药、阵法、符箓等修仙元素
- 境界分明的修炼体系（如筑基、金丹、元婴等）

**写作风格**：
- 古典雅致的文笔，适当使用文言词汇
- 生动的人物描写和对话
- 详细的修炼场景和法术描写
- 引人入胜的情节发展和适当的悬念

**情节要求**：
- 体现修仙世界的奇幻色彩
- 包含修炼、探险、争斗等经典元素
- 适当的冲突和转折点
- 为后续发展留下悬念

章节长度应在2500-3000字之间，确保内容充实完整。
""",

        StoryStyle.WUXIA: """
你是一位专业的武侠小说作家，深谙江湖之道。请创作一个武侠风格的故事章节，包含以下元素：

**世界观要求**：
- 传统江湖世界观，包含武功、门派、恩怨情仇
- 各大武林门派和江湖势力
- 内功心法、武学秘籍、神兵利器

**写作风格**：
- 豪迈洒脱的江湖文风
- 侠义精神和江湖道义的体现
- 精彩的武打场面描写
- 人物性格鲜明，对话生动传神

**情节要求**：
- 体现侠义精神和江湖恩怨
- 包含武功比拼、江湖争斗等元素
- 快意恩仇的情节发展
- 展现江湖儿女的情义

章节长度应在2500-3000字之间，确保武侠韵味浓厚。
""",

        StoryStyle.SCIFI: """
你是一位专业的科幻小说作家，善于构建未来世界。请创作一个科幻风格的故事章节，包含以下元素：

**世界观要求**：
- 未来科技世界观，包含先进科技、太空探索、AI文明
- 科学技术的发展和应用
- 未来社会结构和人类进化

**写作风格**：
- 理性严谨的科学思维
- 科学幻想与人文思考的结合
- 详细的科技设备和原理描述
- 对未来社会的深度思考

**情节要求**：
- 体现科技发展对人类的影响
- 包含科学探索、技术创新等元素
- 紧张刺激的情节发展
- 引发对未来的思考

章节长度应在2500-3000字之间，确保科幻感十足。
"""
    }
    
    # ==================== 世界观生成提示词 ====================
    # 用于生成不同风格故事的世界观设定
    
    WORLDVIEW_PROMPTS = {
        StoryStyle.XIANXIA: """
为修仙小说《{title}》创建完整的世界观框架。请根据标题自动推断故事主题和风格。

请返回JSON格式：
{{
  "world_name": "世界名称（如：青云大陆、九州仙域）",
  "power_system": "修炼体系详述（包含境界划分：炼气、筑基、金丹、元婴、化神、合体、大乘、渡劫、大罗等）",
  "geography": "主要地理环境和重要地点（如：名山大川、仙门圣地、秘境险地）",
  "main_character": "主角基础设定和背景（修炼天赋、出身来历、性格特点）",
  "story_theme": "根据标题推断的故事主题（如：逆天改命、仙路争锋、情缘仙途）",
  "main_plot": "主要情节线索（修炼历程、机缘奇遇、恩怨情仇）",
  "conflict_setup": "核心矛盾冲突（天道束缚、门派争斗、情感纠葛等）"
}}

要求：
1. 根据标题《{title}》智能推断故事主题和背景
2. 创建符合修仙风格的完整世界观，体现仙侠特色
3. 每个字段内容丰富但简洁，100-200字
4. 修炼体系要层次分明，符合传统修仙设定
5. 为故事发展预留充足空间，避免过于具体
""",

        StoryStyle.WUXIA: """
为武侠小说《{title}》创建完整的江湖世界观。请根据标题自动推断故事主题和风格。

请返回JSON格式：
{{
  "world_name": "江湖背景（如：大明江湖、宋朝武林、元末乱世）",
  "power_system": "武功体系和门派设定（内功心法、武学招式、兵器谱排名）",
  "geography": "主要地理环境和江湖重地（名山大川、武林圣地、险恶之所）",
  "main_character": "主角基础设定和江湖身份（武功天赋、师承来历、江湖地位）",
  "story_theme": "根据标题推断的故事主题（如：快意恩仇、侠义天下、武林争霸）",
  "main_plot": "主要情节线索（武功修炼、江湖恩怨、侠义之路）",
  "conflict_setup": "江湖恩怨和核心冲突（门派纷争、武林浩劫、个人恩仇）"
}}

要求：
1. 根据标题《{title}》智能推断故事主题和背景
2. 创建符合武侠风格的完整世界观，体现江湖特色
3. 每个字段内容丰富但简洁，100-200字
4. 武功体系要有层次，体现武林等级
5. 体现侠义精神和江湖道义
""",

        StoryStyle.SCIFI: """
为科幻小说《{title}》创建完整的世界观框架。请根据标题自动推断故事主题和风格。

请返回JSON格式：
{{
  "world_name": "世界背景（如：2157年地球、银河联邦、赛博朋克都市）",
  "power_system": "科技体系和能力设定（科技等级、能力系统、装备体系）",
  "geography": "主要环境和重要地点（太空站、殖民星球、科技都市）",
  "main_character": "主角基础设定和身份背景（科技能力、职业身份、特殊经历）",
  "story_theme": "根据标题推断的故事主题（如：科技觉醒、星际探索、人工智能）",
  "main_plot": "主要情节线索（科技发展、探索发现、文明冲突）",
  "conflict_setup": "科技冲突和核心矛盾（技术伦理、文明碰撞、生存危机）"
}}

要求：
1. 根据标题《{title}》智能推断故事主题和背景
2. 创建符合科幻风格的完整世界观，体现科技感
3. 每个字段内容丰富但简洁，100-200字
4. 科技体系要有逻辑性和前瞻性
5. 体现科技发展对人类社会的影响
"""
    }
    
    # ==================== 标题生成提示词 ====================
    # 用于生成不同风格的故事标题
    
    TITLE_PROMPTS = {
        StoryStyle.XIANXIA: """
生成一个修仙小说的标题，要求：
1. 简洁有力，4-6个字
2. 体现修仙元素（如：道、仙、神、灵、玄、天等）
3. 有吸引力和神秘感
4. 符合修仙小说的命名传统

示例风格：《逆天仙途》《九天玄诀》《仙道独尊》
直接返回标题，不要其他内容。
""",

        StoryStyle.WUXIA: """
生成一个武侠小说的标题，要求：
1. 简洁有力，4-6个字  
2. 体现江湖武侠元素（如：剑、刀、侠、武、江湖等）
3. 有吸引力和豪迈感
4. 符合武侠小说的命名传统

示例风格：《笑傲江湖》《神雕侠侣》《天龙八部》
直接返回标题，不要其他内容。
""",

        StoryStyle.SCIFI: """
生成一个科幻小说的标题，要求：
1. 简洁有力，4-6个字
2. 体现科技未来元素（如：星际、机械、量子、虚拟等）
3. 有吸引力和科技感
4. 符合科幻小说的命名传统

示例风格：《三体》《银河帝国》《机械公敌》
直接返回标题，不要其他内容。
"""
    }

    # ==================== 功能性生成提示词 ====================
    # 用于各种内容生成功能的提示词模板

    GENERATION_PROMPTS = {
        # 章节生成提示词模板（优化版 - 解决章节衔接问题）
        "chapter": """
{style_prompt}

{context}

请基于以上信息，创作下一章节的内容。

**🔗 强制性衔接要求（必须严格执行）：**
1. **开头衔接**：本章开头必须直接承接上章结尾场景，不得重新开始故事
   - 如果上章结尾主角在特定位置，本章必须从该位置继续
   - 如果上章结尾有未完成的动作，本章必须继续该动作
   - 禁止重复描述已知的背景信息（如地点介绍、人物背景等）

2. **选择后果体现**：如果用户做出了选择，本章第一段必须立即展现选择的直接结果
   - 用户选择必须在开头3句话内得到明确体现
   - 选择的后果要符合逻辑，不能敷衍了事

3. **状态连续性**：主角的位置、状态、情绪必须与上章结尾保持连续
   - 不能突然改变主角的位置而不解释
   - 主角的体力、法力、情绪状态要有合理的延续
   - 环境状态要保持一致性

**输出格式要求（必须严格遵守）：**
首先输出章节标题，格式为：
CHAPTER_TITLE: 具体的章节标题（如：血祭危机、乾坤印觉醒、绝境逃生等）

然后输出章节正文内容，直接从故事开始，不要包含任何格式化标记。

**内容要求（必须遵守）：**
1. **字数要求**：章节内容必须在{min_words}-{max_words}字之间，这是硬性要求！
2. **内容质量**：必须是完整的故事章节，包含详细的情节描述、人物对话、环境描写
3. **故事连贯**：严格按照上述衔接要求，确保与前章无缝连接
4. **结构完整**：有明确的开头、发展、高潮、结尾
5. **悬念设置**：章节结尾要为下一个选择做铺垫，但要明确描述结尾状态
6. **标题要求**：章节标题要简洁有力，体现本章核心情节，不超过8个字

**写作技巧要求：**
1. **节奏控制**：合理安排情节节奏，张弛有度
2. **人物塑造**：通过行动、对话、心理活动展现人物性格
3. **环境描写**：适当的环境描写增强代入感
4. **冲突设置**：每章都要有明确的冲突或转折点
5. **情感渲染**：通过细节描写增强情感共鸣
6. **结尾状态描述**：章节结尾要清晰描述主角的当前状态和环境，为下章提供明确起点

**❌ 严格禁止的行为：**
- 重新开始故事或重复背景介绍
- 忽略用户选择的影响
- 突然改变主角位置而不解释
- 与上章情节出现逻辑矛盾
- 模糊的结尾描述

**重要提醒**：
- 请确保生成足够长的内容，至少{min_words}字！不要过早结束！
- 内容要丰富详实：包含详细的环境描写、人物心理、对话交流、动作场面
- 节奏要适中：不要急于推进情节，要有足够的铺垫和描写
- 章节标题要放在最开头，格式：CHAPTER_TITLE: 标题内容
- 正文内容不要包含"第X章"、"**第X章：标题**"等格式化标记
- 直接从故事正文开始

**内容扩展建议**：
- 环境描写：详细描述场景、氛围、视觉效果
- 人物刻画：展现角色的思考过程、情感变化、行为动机
- 对话丰富：增加人物间的对话，推进情节发展
- 动作描写：详细描述战斗、探索、操作等动作场面
- 内心独白：展现主角的思考和决策过程，立即承接上章结尾
""",

        # 选择生成提示词
        "choices": """
基于以下章节内容，生成3个不同的选择选项，让读者决定故事的发展方向。

章节内容：
{chapter_content}

故事风格：{story_style}

要求：
1. **选择差异化**：3个选择要有明显的差异，代表不同的发展方向和风险等级
2. **风格一致**：选择要符合{story_style}风格的特色和氛围
3. **简洁明了**：每个选择都要简洁明了，不超过30字
4. **逻辑合理**：选择要符合当前情节的逻辑，不能突兀
5. **吸引力强**：选择要有吸引力，让读者想要探索不同的可能性

选择类型建议：
- 第一个选择：相对安全/保守的选择
- 第二个选择：平衡风险和收益的选择
- 第三个选择：高风险高回报的选择

返回JSON格式的数组：
["选择1", "选择2", "选择3"]

示例格式（仅供参考，请根据实际情节生成）：
["谨慎探索神秘洞穴", "直接冲入寻找宝藏", "先观察周围环境"]
""",

        # 摘要生成提示词（优化版 - 增强状态描述但保持简单格式）
        "summary": """
请阅读以下完整章节内容，并生成详细的章节摘要：

章节内容：
{chapter_content}

要求：
1. **核心提取**：总结整章的主要情节发展，不要复制开头文字
2. **关键突出**：突出关键事件和重要转折点
3. **状态描述**：必须包含章节结尾时主角的位置、状态和环境情况
4. **便于衔接**：为下一章提供明确的起点信息
5. **简洁明了**：控制在150字以内，语言简洁有力
6. **格式统一**：直接返回摘要文本，不需要其他格式

摘要应该回答以下问题：
- 主角在这一章做了什么？
- 发生了什么关键事件？
- 有什么重要的转折或发现？
- 章节结尾时主角在哪里，处于什么状态？

示例格式：
"林浩在仙灵洞府与守护者激战，经过艰难战斗最终击败对手，获得珍贵的修炼秘籍。章节结尾，林浩站在洞府深处的密室中，虽然体力消耗较大但精神振奋，手持新获得的秘籍，面前还有其他未探索的通道。"
""",

        # 上下文构建提示词（优化版 - 保持原有字段结构）
        "context": """
**故事背景信息：**
- 故事标题：{title}
- 故事风格：{style}
- 当前章节：第{chapter_number}章

**世界观设定：**
{worldview}

**故事发展脉络：**
{story_summary}

**用户选择影响：**
{user_choice_context}

**当前情境：**
{current_situation}
"""
    }

    # ==================== 提示词访问方法 ====================
    # 提供统一的访问接口

    @classmethod
    def get_style_prompt(cls, style: StoryStyle) -> str:
        """获取风格基础提示词"""
        return cls.STYLE_PROMPTS.get(style, cls.STYLE_PROMPTS[StoryStyle.XIANXIA])

    @classmethod
    def get_worldview_prompt(cls, style: StoryStyle, title: str) -> str:
        """获取世界观生成提示词"""
        template = cls.WORLDVIEW_PROMPTS.get(style, cls.WORLDVIEW_PROMPTS[StoryStyle.XIANXIA])
        return template.format(title=title)

    @classmethod
    def get_title_prompt(cls, style: StoryStyle) -> str:
        """获取标题生成提示词"""
        return cls.TITLE_PROMPTS.get(style, cls.TITLE_PROMPTS[StoryStyle.XIANXIA])

    @classmethod
    def get_chapter_prompt(cls, style: StoryStyle, context: str, min_words: int = 2500, max_words: int = 3000) -> str:
        """获取章节生成提示词"""
        style_prompt = cls.get_style_prompt(style)
        template = cls.GENERATION_PROMPTS["chapter"]
        return template.format(
            style_prompt=style_prompt,
            context=context,
            min_words=min_words,
            max_words=max_words
        )

    @classmethod
    def get_choices_prompt(cls, chapter_content: str, story_style: StoryStyle) -> str:
        """获取选择生成提示词"""
        template = cls.GENERATION_PROMPTS["choices"]
        return template.format(
            chapter_content=chapter_content,
            story_style=story_style.value
        )

    @classmethod
    def get_summary_prompt(cls, chapter_content: str) -> str:
        """获取摘要生成提示词"""
        template = cls.GENERATION_PROMPTS["summary"]
        return template.format(chapter_content=chapter_content)

    @classmethod
    def get_context_template(cls) -> str:
        """获取上下文构建模板"""
        return cls.GENERATION_PROMPTS["context"]
