"""
统一提示词管理器

基于集中管理的提示词库，提供统一的访问接口和管理功能
"""

from typing import Dict, Any, List
from app.models.story import StoryStyle
from .unified_prompts import UnifiedPrompts
from app.utils.logger import get_logger

logger = get_logger(__name__)


class UnifiedPromptManager:
    """统一提示词管理器 - 基于集中管理的提示词库"""
    
    def __init__(self):
        self.prompts = UnifiedPrompts
        logger.info("统一提示词管理器初始化完成")
        
    def get_style_prompt(self, style: StoryStyle) -> str:
        """获取风格基础提示词"""
        try:
            prompt = self.prompts.get_style_prompt(style)
            logger.debug(f"获取风格提示词: {style.value}, 长度: {len(prompt)}")
            return prompt
        except Exception as e:
            logger.error(f"获取风格提示词失败: {e}")
            return self.prompts.get_style_prompt(StoryStyle.XIANXIA)
    
    def get_worldview_prompt(self, style: StoryStyle, title: str) -> str:
        """获取世界观生成提示词"""
        try:
            prompt = self.prompts.get_worldview_prompt(style, title)
            logger.debug(f"获取世界观提示词: {style.value}, 标题: {title}")
            return prompt
        except Exception as e:
            logger.error(f"获取世界观提示词失败: {e}")
            return self.prompts.get_worldview_prompt(StoryStyle.XIANXIA, title)
    
    def get_title_prompt(self, style: StoryStyle) -> str:
        """获取标题生成提示词"""
        try:
            prompt = self.prompts.get_title_prompt(style)
            logger.debug(f"获取标题提示词: {style.value}")
            return prompt
        except Exception as e:
            logger.error(f"获取标题提示词失败: {e}")
            return self.prompts.get_title_prompt(StoryStyle.XIANXIA)
    
    def get_chapter_prompt(
        self, 
        style: StoryStyle,
        context: str,
        min_words: int = 2500,
        max_words: int = 3000
    ) -> str:
        """获取章节生成提示词"""
        try:
            prompt = self.prompts.get_chapter_prompt(style, context, min_words, max_words)
            logger.debug(f"获取章节提示词: {style.value}, 字数要求: {min_words}-{max_words}")
            return prompt
        except Exception as e:
            logger.error(f"获取章节提示词失败: {e}")
            raise Exception(f"生成章节提示词失败: {str(e)}")
    
    def get_choices_prompt(self, chapter_content: str, story_style: StoryStyle) -> str:
        """获取选择生成提示词"""
        try:
            prompt = self.prompts.get_choices_prompt(chapter_content, story_style)
            logger.debug(f"获取选择提示词: {story_style.value}")
            return prompt
        except Exception as e:
            logger.error(f"获取选择提示词失败: {e}")
            raise Exception(f"生成选择提示词失败: {str(e)}")
    
    def get_summary_prompt(self, chapter_content: str) -> str:
        """获取摘要生成提示词"""
        try:
            prompt = self.prompts.get_summary_prompt(chapter_content)
            logger.debug("获取摘要提示词")
            return prompt
        except Exception as e:
            logger.error(f"获取摘要提示词失败: {e}")
            raise Exception(f"生成摘要提示词失败: {str(e)}")
    
    def build_context(
        self,
        title: str,
        style: StoryStyle,
        chapter_number: int,
        worldview: Dict[str, Any],
        story_summary: str = "",
        user_choice: str = "",
        current_situation: str = ""
    ) -> str:
        """构建故事上下文（优化版 - 增强章节衔接）"""
        try:
            # 格式化世界观信息
            worldview_text = self._format_worldview(worldview)

            # 构建用户选择上下文（增强版）
            user_choice_context = ""
            if user_choice:
                user_choice_context = f"""**上一章用户选择：** "{user_choice}"

**重要要求：**
- 本章开头必须立即体现这个选择的直接后果
- 选择的影响要在前3句话内明确展现
- 不能忽略或敷衍用户的选择
- 确保选择推动情节发展，保持逻辑连贯性"""
            else:
                user_choice_context = "这是故事的第一章，无用户选择需要承接。"

            # 构建当前情境（增强版）
            current_situation_text = ""
            if current_situation:
                current_situation_text = f"""**当前情境：** {current_situation}

**章节衔接要求：**
- 必须直接承接上章结尾场景，不得重新开始
- 禁止重复已描述的背景信息
- 保持主角位置、状态、情绪的连续性
- 如果上章有未完成的动作，本章必须继续"""
            else:
                current_situation_text = """**章节衔接要求：**
- 这是第一章，需要自然开始故事
- 避免过长的背景介绍，直接进入情节
- 确保开头引人入胜，快速建立故事氛围"""

            template = self.prompts.get_context_template()
            context = template.format(
                title=title,
                style=style.value,
                chapter_number=chapter_number,
                worldview=worldview_text,
                story_summary=story_summary or "故事刚刚开始",
                user_choice_context=user_choice_context,
                current_situation=current_situation_text
            )

            logger.debug(f"构建增强上下文完成: 章节{chapter_number}, 风格{style.value}")
            return context
        except Exception as e:
            logger.error(f"构建上下文失败: {e}")
            raise Exception(f"构建故事上下文失败: {str(e)}")
    
    def _format_worldview(self, worldview: Dict[str, Any]) -> str:
        """格式化世界观信息"""
        if not worldview:
            return "世界观设定待完善"
        
        formatted_parts = []
        
        # 定义字段映射和显示顺序
        field_mapping = {
            "world_name": "世界名称",
            "power_system": "力量体系", 
            "geography": "地理环境",
            "main_character": "主角设定",
            "story_theme": "故事主题",
            "main_plot": "主要情节",
            "conflict_setup": "核心冲突"
        }
        
        for field, display_name in field_mapping.items():
            if field in worldview and worldview[field]:
                formatted_parts.append(f"- **{display_name}**: {worldview[field]}")
        
        return "\n".join(formatted_parts) if formatted_parts else "世界观设定待完善"
    
    def get_supported_styles(self) -> List[StoryStyle]:
        """获取支持的故事风格列表"""
        return list(self.prompts.STYLE_PROMPTS.keys())
    
    def validate_prompt_completeness(self) -> Dict[str, bool]:
        """验证提示词完整性"""
        validation_result = {
            "style_prompts": True,
            "worldview_prompts": True,
            "title_prompts": True,
            "generation_prompts": True
        }
        
        # 检查基础风格提示词
        required_styles = [StoryStyle.XIANXIA, StoryStyle.WUXIA, StoryStyle.SCIFI]
        for style in required_styles:
            if style not in self.prompts.STYLE_PROMPTS:
                validation_result["style_prompts"] = False
                break
        
        # 检查世界观提示词
        for style in required_styles:
            if style not in self.prompts.WORLDVIEW_PROMPTS:
                validation_result["worldview_prompts"] = False
                break
        
        # 检查标题提示词
        for style in required_styles:
            if style not in self.prompts.TITLE_PROMPTS:
                validation_result["title_prompts"] = False
                break
        
        # 检查生成提示词
        required_generation_types = ["chapter", "choices", "summary", "context"]
        for gen_type in required_generation_types:
            if gen_type not in self.prompts.GENERATION_PROMPTS:
                validation_result["generation_prompts"] = False
                break
        
        return validation_result
    
    def get_prompt_statistics(self) -> Dict[str, Any]:
        """获取提示词统计信息"""
        stats = {
            "total_style_prompts": len(self.prompts.STYLE_PROMPTS),
            "total_worldview_prompts": len(self.prompts.WORLDVIEW_PROMPTS),
            "total_title_prompts": len(self.prompts.TITLE_PROMPTS),
            "total_generation_prompts": len(self.prompts.GENERATION_PROMPTS),
            "supported_styles": [style.value for style in self.get_supported_styles()],
            "prompt_lengths": {}
        }
        
        # 计算各类提示词的平均长度
        for style in self.get_supported_styles():
            style_prompt_len = len(self.prompts.get_style_prompt(style))
            worldview_prompt_len = len(self.prompts.get_worldview_prompt(style, "测试标题"))
            title_prompt_len = len(self.prompts.get_title_prompt(style))
            
            stats["prompt_lengths"][style.value] = {
                "style": style_prompt_len,
                "worldview": worldview_prompt_len,
                "title": title_prompt_len
            }
        
        return stats


# 全局统一提示词管理器实例
unified_prompt_manager = UnifiedPromptManager()
