import jwt
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from app.config import settings

# JWT配置
JWT_SECRET_KEY = settings.jwt_secret_key
JWT_ALGORITHM = settings.algorithm
JWT_EXPIRATION_HOURS = 24 * 7  # 7天

class JWTUtils:
    @staticmethod
    def generate_token(user_id: str, username: str) -> str:
        """
        生成JWT令牌
        
        Args:
            user_id: 用户ID
            username: 用户名
            
        Returns:
            JWT令牌字符串
        """
        payload = {
            'user_id': user_id,
            'username': username,
            'exp': datetime.utcnow() + timedelta(hours=JWT_EXPIRATION_HOURS),
            'iat': datetime.utcnow()
        }
        
        return jwt.encode(payload, JWT_SECRET_KEY, algorithm=JWT_ALGORITHM)
    
    @staticmethod
    def verify_token(token: str) -> Optional[Dict[str, Any]]:
        """
        验证JWT令牌
        
        Args:
            token: JWT令牌字符串
            
        Returns:
            解码后的payload，如果验证失败返回None
        """
        try:
            payload = jwt.decode(token, JWT_SECRET_KEY, algorithms=[JWT_ALGORITHM])
            return payload
        except jwt.ExpiredSignatureError:
            return None
        except jwt.InvalidTokenError:
            return None
    
    @staticmethod
    def extract_token_from_header(auth_header: str) -> Optional[str]:
        """
        从Authorization头中提取token
        
        Args:
            auth_header: Authorization头的值
            
        Returns:
            提取的token，如果格式不正确返回None
        """
        if not auth_header:
            return None
        
        parts = auth_header.split(' ')
        if len(parts) != 2 or parts[0].lower() != 'bearer':
            return None
        
        return parts[1]

# FastAPI版本的认证在api/auth.py中实现